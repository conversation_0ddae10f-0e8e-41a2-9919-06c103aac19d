apiVersion: v1
kind: ConfigMap
metadata:
  name: clickhouse-performance-monitor
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: clickhouse
data:
  clickhouse-performance-monitor.json: |
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "description": "ClickHouse Performance Monitor",
      "editable": true,
      "gnetId": 13606,
      "graphTooltip": 1,
      "id": 807,
      "iteration": 1726640261289,
      "links": [],
      "panels": [
        {
          "cacheTimeout": null,
          "colorBackground": false,
          "colorValue": true,
          "colors": [
            "#56A64B",
            "rgba(237, 129, 40, 0.89)",
            "#d44a3a"
          ],
          "datasource": "Altinity plugin for ClickHouse PROD",
          "decimals": null,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "format": "s",
          "gauge": {
            "maxValue": 100,
            "minValue": 0,
            "show": false,
            "thresholdLabels": false,
            "thresholdMarkers": true
          },
          "gridPos": {
            "h": 3,
            "w": 4,
            "x": 0,
            "y": 0
          },
          "id": 26,
          "interval": null,
          "links": [],
          "mappingType": 2,
          "mappingTypes": [
            {
              "name": "value to text",
              "value": 1
            },
            {
              "name": "range to text",
              "value": 2
            }
          ],
          "maxDataPoints": 100,
          "nullPointMode": "connected",
          "nullText": null,
          "postfix": "",
          "postfixFontSize": "50%",
          "prefix": "",
          "prefixFontSize": "50%",
          "rangeMaps": [
            {
              "from": "null",
              "text": "N/A",
              "to": "null"
            }
          ],
          "sparkline": {
            "fillColor": "rgba(31, 118, 189, 0.18)",
            "full": false,
            "lineColor": "rgb(31, 120, 193)",
            "show": false,
            "ymax": null,
            "ymin": null
          },
          "tableColumn": "",
          "targets": [
            {
              "dateTimeType": "DATETIME",
              "extrapolate": true,
              "format": "table",
              "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t",
              "intervalFactor": 1,
              "query": "SELECT uptime()\n",
              "rawQuery": "SELECT uptime()",
              "refId": "A",
              "round": "0s",
              "skip_comments": true
            }
          ],
          "thresholds": "",
          "timeFrom": null,
          "timeShift": null,
          "title": "CK UP Time",
          "type": "singlestat",
          "valueFontSize": "80%",
          "valueMaps": [
            {
              "op": "=",
              "text": "N/A",
              "value": "null"
            },
            {
              "op": "=",
              "text": "",
              "value": ""
            }
          ],
          "valueName": "last_time"
        },
        {
          "cacheTimeout": null,
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {
                "align": null,
                "filterable": false
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "Time"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Time"
                  },
                  {
                    "id": "unit",
                    "value": "time: YYYY-MM-DD HH:mm:ss"
                  },
                  {
                    "id": "custom.align",
                    "value": null
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "Free"
                },
                "properties": [
                  {
                    "id": "unit",
                    "value": "bytes"
                  },
                  {
                    "id": "decimals",
                    "value": 2
                  },
                  {
                    "id": "custom.align",
                    "value": null
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "Total"
                },
                "properties": [
                  {
                    "id": "unit",
                    "value": "bytes"
                  },
                  {
                    "id": "decimals",
                    "value": 2
                  },
                  {
                    "id": "custom.align",
                    "value": null
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "Used"
                },
                "properties": [
                  {
                    "id": "unit",
                    "value": "percentunit"
                  },
                  {
                    "id": "decimals",
                    "value": 2
                  },
                  {
                    "id": "custom.displayMode",
                    "value": "color-background"
                  },
                  {
                    "id": "custom.align",
                    "value": null
                  },
                  {
                    "id": "thresholds",
                    "value": {
                      "mode": "absolute",
                      "steps": [
                        {
                          "color": "rgba(50, 172, 45, 0.97)",
                          "value": null
                        },
                        {
                          "color": "rgba(237, 129, 40, 0.89)",
                          "value": 70
                        },
                        {
                          "color": "rgba(245, 54, 54, 0.9)",
                          "value": 90
                        }
                      ]
                    }
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 3,
            "w": 10,
            "x": 4,
            "y": 0
          },
          "id": 30,
          "links": [],
          "options": {
            "showHeader": true
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "database": "system",
              "dateColDataType": "",
              "dateLoading": false,
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "extrapolate": true,
              "format": "table",
              "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t",
              "intervalFactor": 1,
              "query": "SELECT\n    name as Name,\n    path as Path,\n    free_space as Free,\n    total_space as Total,\n    1 - free_space/total_space as Used\nFROM $table",
              "rawQuery": "SELECT\n    name as Name,\n    path as Path,\n    free_space as Free,\n    total_space as Total,\n    1 - free_space/total_space as Used\nFROM system.disks",
              "refId": "A",
              "round": "0s",
              "skip_comments": true,
              "table": "disks",
              "tableLoading": false
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Disk Space Used Basic",
          "transformations": [
            {
              "id": "merge",
              "options": {
                "reducers": []
              }
            }
          ],
          "type": "table"
        },
        {
          "columns": [],
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fontSize": "100%",
          "gridPos": {
            "h": 3,
            "w": 8,
            "x": 14,
            "y": 0
          },
          "id": 57,
          "pageSize": null,
          "scroll": true,
          "showHeader": true,
          "sort": {
            "col": 0,
            "desc": true
          },
          "styles": [
            {
              "alias": "Time",
              "align": "auto",
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "pattern": "Time",
              "type": "date"
            },
            {
              "alias": "",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "decimals": 2,
              "pattern": "/.*/",
              "thresholds": [],
              "type": "number",
              "unit": "short"
            }
          ],
          "targets": [
            {
              "database": "system",
              "dateColDataType": "",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "extrapolate": true,
              "format": "table",
              "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t",
              "intervalFactor": 1,
              "query": "SELECT\n    value as ReplicasMaxAbsoluteDelay\nFROM $table\n\nWHERE metric='ReplicasMaxAbsoluteDelay'\n\n",
              "rawQuery": "SELECT\n    value as ReplicasMaxAbsoluteDelay\nFROM system.asynchronous_metrics\n\nWHERE metric='ReplicasMaxAbsoluteDelay'",
              "refId": "A",
              "round": "0s",
              "skip_comments": true,
              "table": "asynchronous_metrics",
              "tableLoading": false
            },
            {
              "database": "system",
              "dateColDataType": "",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "extrapolate": true,
              "format": "table",
              "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t",
              "intervalFactor": 1,
              "query": "SELECT\n    value as ReplicasMaxRelativeDelay\nFROM $table\n\nWHERE metric='ReplicasMaxRelativeDelay'\n",
              "rawQuery": "SELECT\n    value as ReplicasMaxRelativeDelay\nFROM system.asynchronous_metrics\n\nWHERE metric='ReplicasMaxRelativeDelay'",
              "refId": "B",
              "round": "0s",
              "skip_comments": true,
              "table": "asynchronous_metrics",
              "tableLoading": false
            },
            {
              "database": "system",
              "dateColDataType": "",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "extrapolate": true,
              "format": "table",
              "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t",
              "intervalFactor": 1,
              "query": "SELECT\n    value as ReplicasMaxQueueSize\nFROM $table\n\nWHERE metric='ReplicasMaxQueueSize'\n",
              "rawQuery": "SELECT\n    value as ReplicasMaxQueueSize\nFROM system.asynchronous_metrics\n\nWHERE metric='ReplicasMaxQueueSize'",
              "refId": "C",
              "round": "0s",
              "skip_comments": true,
              "table": "asynchronous_metrics",
              "tableLoading": false
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Replica Info Base",
          "transform": "table",
          "type": "table-old"
        },
        {
          "cacheTimeout": null,
          "datasource": "Altinity plugin for ClickHouse PROD",
          "description": "",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {
                "align": null,
                "filterable": false
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "Time"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Time"
                  },
                  {
                    "id": "unit",
                    "value": "time: YYYY-MM-DD HH:mm:ss"
                  },
                  {
                    "id": "custom.align",
                    "value": null
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "ColumnCnt"
                },
                "properties": [
                  {
                    "id": "unit",
                    "value": "short"
                  },
                  {
                    "id": "custom.align",
                    "value": null
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "TotalRows"
                },
                "properties": [
                  {
                    "id": "unit",
                    "value": "none"
                  },
                  {
                    "id": "custom.align",
                    "value": null
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "TotalBytes"
                },
                "properties": [
                  {
                    "id": "unit",
                    "value": "bytes"
                  },
                  {
                    "id": "decimals",
                    "value": 2
                  },
                  {
                    "id": "custom.align",
                    "value": null
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 10,
            "w": 11,
            "x": 0,
            "y": 3
          },
          "id": 28,
          "links": [],
          "options": {
            "showHeader": true
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "database": "system",
              "dateColDataType": "",
              "dateLoading": false,
              "dateTimeColDataType": "metadata_modification_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "extrapolate": true,
              "format": "table",
              "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t",
              "intervalFactor": 1,
              "query": "SELECT\n    database as DatabaseName,\n    name as TableName,\n    total_rows as TotalRows,\n    total_bytes as TotalBytes\nFROM $table\n\nWHERE database !='system'\nORDER BY 3 DESC\n",
              "rawQuery": "SELECT\n    database as DatabaseName,\n    name as TableName,\n    total_rows as TotalRows,\n    total_bytes as TotalBytes\nFROM system.tables\n\nWHERE database !='system'\nORDER BY 3 DESC",
              "refId": "B",
              "round": "0s",
              "skip_comments": true,
              "table": "tables",
              "tableLoading": false
            },
            {
              "database": "system",
              "dateColDataType": "",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "extrapolate": true,
              "format": "table",
              "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t",
              "intervalFactor": 1,
              "query": "SELECT\n    database as DatabaseName,\n    table as TableName,\n    count(1) as ColumnCnt\nFROM $table\nWHERE database!='system'\nGROUP BY DatabaseName, TableName",
              "rawQuery": "SELECT\n    database as DatabaseName,\n    table as TableName,\n    count(1) as ColumnCnt\nFROM system.columns\nWHERE database!='system'\nGROUP BY DatabaseName, TableName",
              "refId": "A",
              "round": "0s",
              "skip_comments": true,
              "table": "columns",
              "tableLoading": false
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Table Info Basic",
          "transformations": [
            {
              "id": "merge",
              "options": {
                "reducers": []
              }
            }
          ],
          "type": "table"
        },
        {
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {
                "align": null,
                "filterable": false
              },
              "decimals": 2,
              "displayName": "",
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "Time"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Time"
                  },
                  {
                    "id": "unit",
                    "value": "time: YYYY-MM-DD HH:mm:ss"
                  },
                  {
                    "id": "custom.align",
                    "value": null
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "bytes_allocated"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Bytes Of Allocated"
                  },
                  {
                    "id": "unit",
                    "value": "bytes"
                  },
                  {
                    "id": "decimals",
                    "value": 2
                  },
                  {
                    "id": "custom.align",
                    "value": null
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "query_count"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Query Cnt"
                  },
                  {
                    "id": "unit",
                    "value": "short"
                  },
                  {
                    "id": "custom.align",
                    "value": null
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "element_count"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Element Cnt"
                  },
                  {
                    "id": "unit",
                    "value": "short"
                  },
                  {
                    "id": "custom.align",
                    "value": null
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "hit_rate"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Hit Rate"
                  },
                  {
                    "id": "unit",
                    "value": "percent"
                  },
                  {
                    "id": "decimals",
                    "value": 2
                  },
                  {
                    "id": "custom.align",
                    "value": null
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "load_factor"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Load Factor"
                  },
                  {
                    "id": "unit",
                    "value": "percent"
                  },
                  {
                    "id": "decimals",
                    "value": 2
                  },
                  {
                    "id": "custom.align",
                    "value": null
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 18,
            "x": 0,
            "y": 13
          },
          "id": 53,
          "options": {
            "showHeader": true
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "database": "system",
              "dateColDataType": "",
              "dateLoading": false,
              "dateTimeColDataType": "last_successful_update_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "extrapolate": true,
              "format": "table",
              "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t",
              "intervalFactor": 1,
              "query": "SELECT\n    database DatabaseName,\n    name DictionnaryName,\n    status Status,\n    type Type,\n    bytes_allocated,\n    query_count,\n    element_count,\n    hit_rate,\n    load_factor\nFROM $table\n\nWHERE $timeFilter\n\n",
              "rawQuery": "SELECT\n    (intDiv(toUInt32(event_time), 4) * 4) * 1000 as t,\n    CurrentMetric_MemoryTracking\nFROM system.metric_log\nWHERE event_date >= toDate(1609221187) AND event_time >= toDateTime(1609221187)\nORDER BY t",
              "refId": "A",
              "round": "0s",
              "skip_comments": true,
              "table": "dictionaries",
              "tableLoading": false
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Dictionaries Info Base",
          "transformations": [
            {
              "id": "merge",
              "options": {
                "reducers": []
              }
            }
          ],
          "type": "table"
        },
        {
          "columns": [],
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fontSize": "100%",
          "gridPos": {
            "h": 8,
            "w": 18,
            "x": 0,
            "y": 21
          },
          "id": 55,
          "pageSize": null,
          "scroll": true,
          "showHeader": true,
          "sort": {
            "col": 0,
            "desc": true
          },
          "styles": [
            {
              "alias": "Time",
              "align": "auto",
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "pattern": "Time",
              "type": "date"
            },
            {
              "alias": "Partition Name",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": 2,
              "mappingType": 1,
              "pattern": "partition",
              "thresholds": [],
              "type": "string",
              "unit": "short"
            },
            {
              "alias": "Data Part Name",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": 2,
              "mappingType": 1,
              "pattern": "name",
              "thresholds": [],
              "type": "number",
              "unit": "short"
            },
            {
              "alias": "Rows",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": 2,
              "mappingType": 1,
              "pattern": "rows",
              "thresholds": [],
              "type": "number",
              "unit": "short"
            },
            {
              "alias": "Database Name",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": 2,
              "mappingType": 1,
              "pattern": "database",
              "thresholds": [],
              "type": "string",
              "unit": "short"
            },
            {
              "alias": "Table Name",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": 2,
              "mappingType": 1,
              "pattern": "table",
              "thresholds": [],
              "type": "string",
              "unit": "short"
            },
            {
              "alias": "Part Type",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": 2,
              "mappingType": 1,
              "pattern": "part_type",
              "thresholds": [],
              "type": "string",
              "unit": "short"
            },
            {
              "alias": "Bytes On Disk",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": 2,
              "mappingType": 1,
              "pattern": "bytes_on_disk",
              "thresholds": [],
              "type": "number",
              "unit": "bytes"
            },
            {
              "alias": "Data Compressed Bytes",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": 2,
              "mappingType": 1,
              "pattern": "data_compressed_bytes",
              "thresholds": [],
              "type": "number",
              "unit": "bytes"
            },
            {
              "alias": "",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "decimals": 2,
              "pattern": "/.*/",
              "thresholds": [],
              "type": "string",
              "unit": "short"
            }
          ],
          "targets": [
            {
              "database": "system",
              "dateColDataType": "",
              "dateLoading": false,
              "dateTimeColDataType": "delete_ttl_info_max",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "extrapolate": true,
              "format": "table",
              "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t",
              "intervalFactor": 1,
              "query": "SELECT\n    database,\n    table,\n    partition,\n    name,\n    part_type,\n    rows,\n    bytes_on_disk,\n    data_compressed_bytes\nFROM $table\n\nWHERE database!='system'\n",
              "rawQuery": "SELECT\n    database,\n    table,\n    partition,\n    name,\n    part_type,\n    rows,\n    bytes_on_disk,\n    data_compressed_bytes\nFROM system.parts\n\nWHERE database!='system'",
              "refId": "A",
              "round": "0s",
              "skip_comments": true,
              "table": "parts",
              "tableLoading": false
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Part Info Base",
          "transform": "table",
          "type": "table-old"
        },
        {
          "collapsed": false,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 29
          },
          "id": 22,
          "panels": [],
          "repeat": null,
          "title": "Top charts",
          "type": "row"
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "thresholds": {
                "mode": "absolute",
                "steps": []
              },
              "unit": "short"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 7,
            "w": 20,
            "x": 0,
            "y": 30
          },
          "hiddenSeries": false,
          "id": 15,
          "legend": {
            "alignAsTable": true,
            "avg": true,
            "current": false,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sort": "avg",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "links": [],
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 5,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "database": "system",
              "dateColDataType": "event_date",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "expr": "",
              "extrapolate": true,
              "format": "time_series",
              "formattedQuery": "<font color=\"darkcyan\">$rateColumns</font>(<br />Â Â Â Â <font color=\"navajowhite\">substring</font>(query,Â Â <font color=\"cornflowerblue\">1</font>,Â Â <font color=\"cornflowerblue\">45</font>) <font color=\"darkorange\">AS</font> query,<br />Â Â Â Â <font color=\"navajowhite\">count</font>() c)<br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">WHERE</font><br />Â Â Â Â <font color=\"navajowhite\">cityHash64</font>(query) <font color=\"darkorange\">global</font> <font color=\"darkorange\">in</font> (<br />Â Â Â Â <font color=\"darkorange\">SELECT</font> <font color=\"navajowhite\">cityHash64</font>(<font color=\"navajowhite\">substring</font>(query,Â Â <font color=\"cornflowerblue\">1</font>,Â Â <font color=\"cornflowerblue\">45</font>)) <font color=\"darkorange\">AS</font> h<br />Â Â Â Â <font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br />Â Â Â Â <font color=\"darkorange\">WHERE</font><br />Â Â Â Â Â Â Â Â <font color=\"darkcyan\">$timeFilter</font><br />Â Â Â Â Â Â Â Â <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$type</font>)<br />Â Â Â Â Â Â Â Â <font color=\"yellow\">AND</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br />Â Â Â Â Â Â Â Â <font color=\"yellow\">AND</font>(<font color=\"lightgreen\">'$query_type'</font> <font color=\"yellow\">=</font> <font color=\"lightgreen\">'all'</font> <font color=\"yellow\">or</font>(positionCaseInsensitive(query,Â Â <font color=\"lightgreen\">'$query_type'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font>))<br />Â Â Â Â <font color=\"darkorange\">GROUP BY</font> h<br />Â Â Â Â <font color=\"darkorange\">ORDER BY</font> <font color=\"navajowhite\">count</font>() <font color=\"darkorange\">desc</font><br />Â Â Â Â <font color=\"darkorange\">LIMIT</font> <font color=\"darkcyan\">$top</font>)<br />Â Â Â Â <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$type</font>)<br />Â Â Â Â <font color=\"yellow\">AND</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br />Â Â Â Â <font color=\"yellow\">AND</font>(<font color=\"lightgreen\">'$query_type'</font> <font color=\"yellow\">=</font> <font color=\"lightgreen\">'all'</font> <font color=\"yellow\">or</font>(positionCaseInsensitive(query,Â Â <font color=\"lightgreen\">'$query_type'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font>))",
              "intervalFactor": 2,
              "query": "$rateColumns(\n    substring(query,  1,  45) AS query,\n    count() c)\nFROM $table\nWHERE\n    cityHash64(query) global in (\n    SELECT cityHash64(substring(query,  1,  45)) AS h\n    FROM $table\n    WHERE\n        $timeFilter\n        AND type in ($type)\n        AND initial_user in ($user)\n        AND('$query_type' = 'all' or(positionCaseInsensitive(query,  '$query_type') = 1))\n    GROUP BY h\n    ORDER BY count() desc\n    LIMIT $top)\n    AND type in ($type)\n    AND initial_user in ($user)\n    AND('$query_type' = 'all' or(positionCaseInsensitive(query,  '$query_type') = 1))",
              "rawQuery": "SELECT t, arrayMap(a -> (a.1, a.2/runningDifference( t/1000 )), groupArr) FROM (SELECT t, groupArray((query, c)) AS groupArr FROM ( SELECT (intDiv(toUInt32(event_time), 600) * 600) * 1000 AS t, substring(query, 1, 45) AS query, count() c FROM system.query_log\nWHERE event_date >= toDate(1715708735) AND event_date <= toDate(1716313535) AND event_time >= toDateTime(1715708735) AND event_time <= toDateTime(1716313535) AND\n    cityHash64(query) global in (\n    SELECT cityHash64(substring(query,  1,  45)) AS h\n    FROM system.query_log\n    WHERE event_date >= toDate(1715708735) AND event_date <= toDate(1716313535) AND event_time >= toDateTime(1715708735) AND event_time <= toDateTime(1716313535) AND\n        event_date >= toDate(1715708735) AND event_date <= toDate(1716313535) AND event_time >= toDateTime(1715708735) AND event_time <= toDateTime(1716313535)\n        AND type in (1,2,3,4)\n        AND initial_user in ('')\n        AND('all' = 'all' or(positionCaseInsensitive(query,  'all') = 1))\n    GROUP BY h\n    ORDER BY count() desc\n    LIMIT 30)\n    AND type in (1,2,3,4)\n    AND initial_user in ('')\n    AND('all' = 'all' or(positionCaseInsensitive(query,  'all') = 1)) GROUP BY t, query ORDER BY t, query) GROUP BY t ORDER BY t)",
              "refId": "A",
              "resultFormat": "time_series",
              "round": "0s",
              "skip_comments": true,
              "table": "query_log",
              "tableLoading": false
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Top $top request's rate by type: $type; user: $user; query type: $query_type",
          "tooltip": {
            "shared": true,
            "sort": 2,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "gridPos": {
            "h": 6,
            "w": 3,
            "x": 20,
            "y": 30
          },
          "id": 17,
          "links": [],
          "options": {
            "content": "1 - successful start of query execution\n\n2 - successful end of query execution\n\n3 - exception before start of query execution\n\n4 - exception while query execution",
            "mode": "markdown"
          },
          "pluginVersion": "7.4.2",
          "title": "Types",
          "transparent": true,
          "type": "text"
        },
        {
          "columns": [],
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fontSize": "100%",
          "gridPos": {
            "h": 10,
            "w": 8,
            "x": 0,
            "y": 37
          },
          "height": "400px",
          "id": 18,
          "links": [],
          "pageSize": null,
          "scroll": true,
          "showHeader": true,
          "sort": {
            "col": 2,
            "desc": true
          },
          "styles": [
            {
              "alias": "Time",
              "align": "auto",
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "pattern": "Time",
              "type": "hidden"
            },
            {
              "alias": "duration",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": 2,
              "pattern": "duration",
              "thresholds": [],
              "type": "number",
              "unit": "ms"
            },
            {
              "alias": "",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": null,
              "pattern": "count",
              "thresholds": [],
              "type": "number",
              "unit": "short"
            },
            {
              "alias": "",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "decimals": 2,
              "pattern": "/.*/",
              "thresholds": [],
              "type": "number",
              "unit": "short"
            }
          ],
          "targets": [
            {
              "database": "system",
              "dateColDataType": "event_date",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "datetimeLoading": false,
              "expr": "",
              "format": "table",
              "formattedQuery": "<font color=\"darkorange\">SELECT</font><br />Â Â Â Â <font color=\"navajowhite\">rand</font>() <font color=\"darkorange\">as</font> t,<br />Â Â Â Â <font color=\"navajowhite\">substring</font>(query,Â Â <font color=\"cornflowerblue\">1</font>,Â Â <font color=\"cornflowerblue\">70</font>) <font color=\"darkorange\">AS</font> query,<br />Â Â Â Â <font color=\"navajowhite\">avg</font>(query_duration_ms) duration,<br />Â Â Â Â <font color=\"navajowhite\">count</font>() <font color=\"navajowhite\">count</font><br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">WHERE</font><br />Â Â Â Â <font color=\"darkcyan\">$timeFilter</font><br />Â Â Â Â <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$type</font>)<br />Â Â Â Â <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br />Â Â Â Â <font color=\"yellow\">and</font>(<font color=\"lightgreen\">'$query_type'</font> <font color=\"yellow\">=</font> <font color=\"lightgreen\">'all'</font> <font color=\"yellow\">or</font>(positionCaseInsensitive(query,Â Â <font color=\"lightgreen\">'$query_type'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font>))<br /><font color=\"darkorange\">GROUP BY</font> query<br /><font color=\"darkorange\">ORDER BY</font> duration <font color=\"darkorange\">desc</font><br /><font color=\"darkorange\">LIMIT</font> <font color=\"darkcyan\">$top</font>",
              "intervalFactor": 2,
              "query": "SELECT\n    rand() as t,\n    substring(query,  1,  70) AS query,\n    avg(query_duration_ms) duration,\n    count() count\nFROM $table\nWHERE\n    $timeFilter\n    AND type in ($type)\n    and initial_user in ($user)\n    and('$query_type' = 'all' or(positionCaseInsensitive(query,  '$query_type') = 1))\nGROUP BY query\nORDER BY duration desc\nLIMIT $top",
              "rawQuery": "SELECT     rand() as t,     substring(query,  1,  70) AS query,     avg(query_duration_ms) duration,     count() count FROM system.query_log WHERE     event_date >= toDate(1498209947) AND event_time >= toDateTime(1498209947)     AND type in (1,2,3,4)     and initial_user in ('default')     and('all' = 'all' or(positionCaseInsensitive(query,  'all') = 1)) GROUP BY query ORDER BY duration desc LIMIT 5",
              "refId": "A",
              "resultFormat": "time_series",
              "table": "query_log",
              "tableLoading": false
            }
          ],
          "title": "Top slow queries by type: $type; user: $user; query type: $query_type",
          "transform": "table",
          "type": "table-old"
        },
        {
          "columns": [],
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fontSize": "100%",
          "gridPos": {
            "h": 10,
            "w": 8,
            "x": 8,
            "y": 37
          },
          "height": "400px",
          "id": 19,
          "links": [],
          "pageSize": null,
          "scroll": true,
          "showHeader": true,
          "sort": {
            "col": 2,
            "desc": true
          },
          "styles": [
            {
              "alias": "Time",
              "align": "auto",
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "pattern": "Time",
              "type": "hidden"
            },
            {
              "alias": "usage",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": 2,
              "pattern": "usage",
              "thresholds": [],
              "type": "number",
              "unit": "bytes"
            },
            {
              "alias": "",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": null,
              "pattern": "count",
              "thresholds": [],
              "type": "number",
              "unit": "short"
            },
            {
              "alias": "",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "decimals": 2,
              "pattern": "/.*/",
              "thresholds": [],
              "type": "number",
              "unit": "short"
            }
          ],
          "targets": [
            {
              "database": "system",
              "dateColDataType": "event_date",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "datetimeLoading": false,
              "expr": "",
              "format": "table",
              "formattedQuery": "<font color=\"darkorange\">SELECT</font><br />Â Â Â Â <font color=\"navajowhite\">rand</font>() <font color=\"darkorange\">as</font> t,<br />Â Â Â Â <font color=\"navajowhite\">substring</font>(query,Â Â <font color=\"cornflowerblue\">1</font>,Â Â <font color=\"cornflowerblue\">70</font>) <font color=\"darkorange\">AS</font> query,<br />Â Â Â Â <font color=\"navajowhite\">avg</font>(memory_usage) usage,<br />Â Â Â Â <font color=\"navajowhite\">count</font>() <font color=\"navajowhite\">count</font><br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">WHERE</font><br />Â Â Â Â <font color=\"darkcyan\">$timeFilter</font><br />Â Â Â Â <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$type</font>)<br />Â Â Â Â <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br />Â Â Â Â <font color=\"yellow\">and</font>(<font color=\"lightgreen\">'$query_type'</font> <font color=\"yellow\">=</font> <font color=\"lightgreen\">'all'</font> <font color=\"yellow\">or</font>(positionCaseInsensitive(query,Â Â <font color=\"lightgreen\">'$query_type'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font>))<br /><font color=\"darkorange\">GROUP BY</font> query<br /><font color=\"darkorange\">ORDER BY</font> usage <font color=\"darkorange\">desc</font><br /><font color=\"darkorange\">LIMIT</font> <font color=\"darkcyan\">$top</font>",
              "intervalFactor": 2,
              "query": "SELECT\n    rand() as t,\n    substring(query,  1,  70) AS query,\n    avg(memory_usage) usage,\n    count() count\nFROM $table\nWHERE\n    $timeFilter\n    AND type in ($type)\n    and initial_user in ($user)\n    and('$query_type' = 'all' or(positionCaseInsensitive(query,  '$query_type') = 1))\nGROUP BY query\nORDER BY usage desc\nLIMIT $top",
              "rawQuery": "SELECT     rand() as t,     substring(query,  1,  70) AS query,     avg(memory_usage) usage,     count() count FROM system.query_log WHERE     event_date >= toDate(1498209947) AND event_time >= toDateTime(1498209947)     AND type in (1,2,3,4)     and initial_user in ('default')     and('all' = 'all' or(positionCaseInsensitive(query,  'all') = 1)) GROUP BY query ORDER BY usage desc LIMIT 5",
              "refId": "A",
              "resultFormat": "time_series",
              "table": "query_log",
              "tableLoading": false
            }
          ],
          "title": "Top memory consumers by type: $type; user: $user; query type: $query_type",
          "transform": "table",
          "type": "table-old"
        },
        {
          "columns": [],
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fontSize": "100%",
          "gridPos": {
            "h": 10,
            "w": 8,
            "x": 16,
            "y": 37
          },
          "height": "400px",
          "id": 20,
          "links": [],
          "pageSize": null,
          "scroll": true,
          "showHeader": true,
          "sort": {
            "col": 3,
            "desc": true
          },
          "styles": [
            {
              "alias": "Time",
              "align": "auto",
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "pattern": "Time",
              "type": "hidden"
            },
            {
              "alias": "type",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": null,
              "pattern": "type",
              "thresholds": [],
              "type": "number",
              "unit": "none"
            },
            {
              "alias": "",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": null,
              "pattern": "count",
              "thresholds": [],
              "type": "number",
              "unit": "short"
            },
            {
              "alias": "",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "decimals": 2,
              "pattern": "/.*/",
              "thresholds": [],
              "type": "number",
              "unit": "short"
            }
          ],
          "targets": [
            {
              "database": "system",
              "dateColDataType": "event_date",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "datetimeLoading": false,
              "expr": "",
              "format": "table",
              "formattedQuery": "<font color=\"darkorange\">SELECT</font><br />Â Â Â Â <font color=\"navajowhite\">rand</font>() <font color=\"darkorange\">as</font> t,<br />Â Â Â Â <font color=\"navajowhite\">substring</font>(query,Â Â <font color=\"cornflowerblue\">1</font>,Â Â <font color=\"cornflowerblue\">70</font>) <font color=\"darkorange\">AS</font> query,<br />Â Â Â Â <font color=\"darkorange\">type</font>,<br />Â Â Â Â <font color=\"navajowhite\">count</font>() <font color=\"navajowhite\">count</font><br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">WHERE</font><br />Â Â Â Â <font color=\"darkcyan\">$timeFilter</font><br />Â Â Â Â <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"darkorange\">in</font> (<font color=\"cornflowerblue\">3</font>,<font color=\"cornflowerblue\">4</font>)<br />Â Â Â Â <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br />Â Â Â Â <font color=\"yellow\">and</font>(<font color=\"lightgreen\">'$query_type'</font> <font color=\"yellow\">=</font> <font color=\"lightgreen\">'all'</font> <font color=\"yellow\">or</font>(positionCaseInsensitive(query,Â Â <font color=\"lightgreen\">'$query_type'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font>))<br /><font color=\"darkorange\">GROUP BY</font><br />Â Â Â Â query,<br />Â Â Â Â <font color=\"darkorange\">type</font><br /><font color=\"darkorange\">ORDER BY</font> <font color=\"navajowhite\">count</font> <font color=\"darkorange\">desc</font><br /><font color=\"darkorange\">LIMIT</font> <font color=\"darkcyan\">$top</font>",
              "intervalFactor": 2,
              "query": "SELECT\n    rand() as t,\n    substring(query,  1,  70) AS query,\n    type,\n    count() count\nFROM $table\nWHERE\n    $timeFilter\n    AND type in (3,4)\n    and initial_user in ($user)\n    and('$query_type' = 'all' or(positionCaseInsensitive(query,  '$query_type') = 1))\nGROUP BY\n    query,\n    type\nORDER BY count desc\nLIMIT $top",
              "rawQuery": "SELECT     rand() as t,     substring(query,  1,  70) AS query,     type,     count() count FROM system.query_log WHERE     event_date >= toDate(1498209947) AND event_time >= toDateTime(1498209947)     AND type in (3,4)     and initial_user in ('default')     and('all' = 'all' or(positionCaseInsensitive(query,  'all') = 1)) GROUP BY     query,     type ORDER BY count desc LIMIT 5",
              "refId": "A",
              "resultFormat": "time_series",
              "table": "query_log",
              "tableLoading": false
            }
          ],
          "title": "Top failed queries by user: $user; query type: $query_type",
          "transform": "table",
          "type": "table-old"
        },
        {
          "collapsed": false,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 47
          },
          "id": 23,
          "panels": [],
          "repeat": null,
          "title": "Request charts",
          "type": "row"
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 7,
            "w": 12,
            "x": 0,
            "y": 48
          },
          "hiddenSeries": false,
          "id": 14,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "links": [],
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 5,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "database": "system",
              "dateColDataType": "event_date",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "expr": "",
              "extrapolate": true,
              "format": "time_series",
              "formattedQuery": "<font color=\"darkcyan\">$rate</font>(<font color=\"navajowhite\">count</font>() c)<br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">where</font>Â Â <font color=\"darkorange\">type</font> <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$type</font>)<br />Â Â Â Â <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br />Â Â Â Â <font color=\"yellow\">and</font>(<font color=\"lightgreen\">'$query_type'</font> <font color=\"yellow\">=</font> <font color=\"lightgreen\">'all'</font> <font color=\"yellow\">or</font>(positionCaseInsensitive(query,Â Â <font color=\"lightgreen\">'$query_type'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font>))",
              "intervalFactor": 2,
              "query": "$rate(count() c)\nFROM $table\nwhere  type in ($type)\n    and initial_user in ($user)\n    and('$query_type' = 'all' or(positionCaseInsensitive(query,  '$query_type') = 1))",
              "rawQuery": "SELECT\n    (intDiv(toUInt32(toDateTime(concat(substr(toString(event_time), 1, 16), ':00'))), 2) * 2) * 1000 as t,\n    avg(ProfileEvent_MergedRows) as ProfileEvent_MergedRows\nFROM system.metric_log\n\nWHERE event_time >= toDateTime(1609221193)\n\nGROUP BY t\n\nORDER BY t",
              "refId": "A",
              "resultFormat": "time_series",
              "round": "0s",
              "skip_comments": true,
              "table": "query_log",
              "tableLoading": false
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Reqs/s by type: $type; user: $user; query type: $query_type",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 7,
            "w": 12,
            "x": 12,
            "y": 48
          },
          "hiddenSeries": false,
          "id": 16,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "links": [],
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 5,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [
            {
              "alias": "insert_duration",
              "yaxis": 2
            }
          ],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "database": "system",
              "dateColDataType": "event_date",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "expr": "",
              "extrapolate": true,
              "format": "time_series",
              "formattedQuery": "<font color=\"darkorange\">SELECT</font><br />Â Â Â Â <font color=\"darkcyan\">$timeSeries</font> <font color=\"darkorange\">as</font> t,<br />Â Â Â Â <font color=\"navajowhite\">avg</font>(query_duration_ms)Â Â select_duration<br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">WHERE</font><br />Â Â Â Â <font color=\"darkcyan\">$timeFilter</font><br />Â Â Â Â <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">2</font><br />Â Â Â Â <font color=\"yellow\">and</font> positionCaseInsensitive(query,Â Â <font color=\"lightgreen\">'select'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font><br />Â Â Â Â <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br /><font color=\"darkorange\">GROUP BY</font> t<br /><font color=\"darkorange\">ORDER BY</font> t",
              "intervalFactor": 2,
              "query": "SELECT\n    $timeSeries as t,\n    avg(query_duration_ms)  select_duration\nFROM $table\nWHERE\n    $timeFilter\n    AND type = 2\n    and positionCaseInsensitive(query,  'select') = 1\n    and initial_user in ($user)\nGROUP BY t\nORDER BY t",
              "rawQuery": "SELECT\n    (intDiv(toUInt32(event_time), 4) * 4) * 1000 as t,\n    avg(query_duration_ms)  select_duration\nFROM system.query_log\nWHERE\n    event_date >= toDate(1608193103) AND event_time >= toDateTime(1608193103)\n    AND type = 2\n    and positionCaseInsensitive(query,  'select') = 1\n    and initial_user in ('u_ops','default')\nGROUP BY t\nORDER BY t",
              "refId": "A",
              "resultFormat": "time_series",
              "round": "0s",
              "skip_comments": true,
              "table": "query_log",
              "tableLoading": false
            },
            {
              "database": "system",
              "dateColDataType": "event_date",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "expr": "",
              "extrapolate": true,
              "format": "time_series",
              "formattedQuery": "<font color=\"darkorange\">SELECT</font><br />Â Â Â Â <font color=\"darkcyan\">$timeSeries</font> <font color=\"darkorange\">as</font> t,<br />Â Â Â Â <font color=\"navajowhite\">avg</font>(query_duration_ms) insert_duration<br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">WHERE</font><br />Â Â Â Â <font color=\"darkcyan\">$timeFilter</font><br />Â Â Â Â <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">2</font><br /><font color=\"yellow\">and</font> positionCaseInsensitive(query,Â Â <font color=\"lightgreen\">'insert into'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font><br />Â Â Â Â <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br /><font color=\"darkorange\">GROUP BY</font> t<br /><font color=\"darkorange\">ORDER BY</font> t",
              "intervalFactor": 2,
              "query": "SELECT\n    $timeSeries as t,\n    avg(query_duration_ms) insert_duration\nFROM $table\nWHERE\n    $timeFilter\n    AND type = 2\nand positionCaseInsensitive(query,  'insert into') = 1\n    and initial_user in ($user)\nGROUP BY t\nORDER BY t",
              "rawQuery": "SELECT\n    (intDiv(toUInt32(event_time), 4) * 4) * 1000 as t,\n    avg(query_duration_ms) insert_duration\nFROM system.query_log\nWHERE\n    event_date >= toDate(1608193175) AND event_time >= toDateTime(1608193175)\n    AND type = 2\nand positionCaseInsensitive(query,  'insert into') = 1\n    and initial_user in ('u_ops','default')\nGROUP BY t\nORDER BY t",
              "refId": "B",
              "resultFormat": "time_series",
              "round": "0s",
              "skip_comments": true,
              "table": "query_log",
              "tableLoading": false
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Query duration by type: $type; user: $user",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "ms",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "ms",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "collapsed": false,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 55
          },
          "id": 24,
          "panels": [],
          "repeat": null,
          "title": "Query log table",
          "type": "row"
        },
        {
          "columns": [],
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fontSize": "100%",
          "gridPos": {
            "h": 7,
            "w": 24,
            "x": 0,
            "y": 56
          },
          "id": 21,
          "links": [],
          "pageSize": null,
          "scroll": true,
          "showHeader": true,
          "sort": {
            "col": 3,
            "desc": true
          },
          "styles": [
            {
              "alias": "Time",
              "align": "auto",
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "link": false,
              "pattern": "Time",
              "type": "date"
            },
            {
              "alias": "",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": 2,
              "pattern": "Duration",
              "thresholds": [],
              "type": "number",
              "unit": "ms"
            },
            {
              "alias": "",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": 2,
              "pattern": "Memory",
              "thresholds": [],
              "type": "number",
              "unit": "bytes"
            },
            {
              "alias": "",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "decimals": 2,
              "pattern": "ReadRows",
              "thresholds": [],
              "type": "number",
              "unit": "short"
            },
            {
              "alias": "",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": 2,
              "mappingType": 1,
              "pattern": "ReadBytes",
              "thresholds": [],
              "type": "number",
              "unit": "bytes"
            },
            {
              "alias": "",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": 2,
              "mappingType": 1,
              "pattern": "/.*/",
              "thresholds": [],
              "type": "number",
              "unit": "short"
            }
          ],
          "targets": [
            {
              "database": "system",
              "dateColDataType": "event_date",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "extrapolate": true,
              "format": "time_series",
              "formattedQuery": "<font color=\"darkorange\">SELECT</font><br />Â Â Â Â event_time,<br />Â Â Â Â user,<br />Â Â Â Â query_duration_ms duration,<br />Â Â Â Â memory_usage memory,<br />Â Â Â Â if(exception<font color=\"yellow\">!=</font><font color=\"lightgreen\">''</font>, <font color=\"lightgreen\">'fail'</font>, <font color=\"lightgreen\">'success'</font>) result,<br />Â Â Â Â <font color=\"navajowhite\">concat</font>(<font color=\"navajowhite\">substring</font>(query,<font color=\"cornflowerblue\">1</font>,<font color=\"cornflowerblue\">120</font>), <font color=\"lightgreen\">'...'</font>) query<br /><font color=\"darkorange\">FROM</font> <font color=\"darkcyan\">$table</font><br /><font color=\"darkorange\">WHERE</font> <font color=\"darkcyan\">$timeFilter</font><br />Â Â Â Â <font color=\"yellow\">AND</font> <font color=\"darkorange\">type</font> <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$type</font>)<br />Â Â Â Â <font color=\"yellow\">and</font> initial_user <font color=\"darkorange\">in</font> (<font color=\"darkcyan\">$user</font>)<br />Â Â Â Â <font color=\"yellow\">and</font>(<font color=\"lightgreen\">'$query_type'</font> <font color=\"yellow\">=</font> <font color=\"lightgreen\">'all'</font> <font color=\"yellow\">or</font>(positionCaseInsensitive(query,Â Â <font color=\"lightgreen\">'$query_type'</font>) <font color=\"yellow\">=</font> <font color=\"cornflowerblue\">1</font>))<br /><font color=\"darkorange\">ORDER BY</font> event_time <font color=\"darkorange\">DESC</font><br /><font color=\"darkorange\">LIMIT</font> <font color=\"cornflowerblue\">1000</font>",
              "intervalFactor": 1,
              "query": "SELECT\n    event_time as EventTime,\n    concat(substring(query,1,120), '...') Query,\n    if(exception!='', 'fail', 'success') Result,\n    user as Operator,\n    query_duration_ms as Duration,\n    memory_usage as Memory,\n    read_rows as ReadRows,\n    read_bytes as ReadBytes\nFROM $table\nWHERE $timeFilter\n    AND type in ($type)\n    and initial_user in ($user)\n    and('$query_type' = 'all' or(positionCaseInsensitive(query,  '$query_type') = 1))\nORDER BY EventTime DESC\nLIMIT 1000",
              "rawQuery": "SELECT\n    (intDiv(toUInt32(event_time), 2) * 2) * 1000 as t,\n    ProfileEvent_MergedRows\nFROM system.metric_log\n\nWHERE event_time >= toDateTime(1608193667)",
              "refId": "A",
              "resultFormat": "time_series",
              "round": "0s",
              "skip_comments": true,
              "table": "query_log",
              "tableLoading": false
            }
          ],
          "title": "Query log by type: $type; user: $user; query type: $query_type",
          "transform": "timeseries_to_columns",
          "type": "table-old"
        },
        {
          "columns": [],
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fontSize": "100%",
          "gridPos": {
            "h": 6,
            "w": 24,
            "x": 0,
            "y": 63
          },
          "id": 51,
          "pageSize": null,
          "scroll": true,
          "showHeader": true,
          "sort": {
            "col": 2,
            "desc": true
          },
          "styles": [
            {
              "alias": "Time",
              "align": "auto",
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "pattern": "Time",
              "type": "date"
            },
            {
              "alias": "",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "decimals": 2,
              "pattern": "Duration",
              "thresholds": [],
              "type": "number",
              "unit": "ms"
            },
            {
              "alias": "",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": 1,
              "link": false,
              "mappingType": 1,
              "pattern": "ReadRows",
              "thresholds": [],
              "type": "number",
              "unit": "short"
            },
            {
              "alias": "",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": 2,
              "mappingType": 1,
              "pattern": "ReadBytes",
              "thresholds": [],
              "type": "number",
              "unit": "bytes"
            }
          ],
          "targets": [
            {
              "database": "system",
              "dateColDataType": "",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "extrapolate": true,
              "format": "table",
              "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t",
              "intervalFactor": 1,
              "query": "SELECT\n    event_time as EventTime,\n    substring(query, 1, 140) as Query,\n    user as Operator,\n    query_duration_ms as Duration,\n    read_rows as ReadRows,\n    read_bytes as ReadBytes\n\nFROM $table\n\nWHERE $timeFilter\n\nORDER BY Duration DESC\n\nLIMIT $top\n",
              "rawQuery": "SELECT\n    (intDiv(toUInt32(event_time), 2) * 2) * 1000 as t,\n    ProfileEvent_InsertedRows\nFROM system.metric_log\nWHERE event_date >= toDate(1608193246) AND event_time >= toDateTime(1608193246)\nORDER BY t",
              "refId": "A",
              "round": "0s",
              "skip_comments": true,
              "table": "query_log",
              "tableLoading": false
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "TimeRange Top $top Slow Query Basic",
          "transform": "table",
          "type": "table-old"
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 69
          },
          "hiddenSeries": false,
          "id": 32,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": false,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "database": "system",
              "dateColDataType": "event_date",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "extrapolate": true,
              "format": "time_series",
              "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t",
              "intervalFactor": 1,
              "query": "SELECT\n    toInt32(date_trunc('minute', toDateTime(event_time))) * 1000 as t,\n    avg(ProfileEvent_OSCPUWaitMicroseconds) as ProfileEvent_OSCPUWaitMicroseconds\nFROM $table\nWHERE $timeFilter\nGROUP BY t\nORDER BY t\n",
              "rawQuery": "SELECT\n    toInt32(date_trunc('minute', toDateTime(event_time))) * 1000 as t,\n    avg(ProfileEvent_OSCPUWaitMicroseconds) as ProfileEvent_OSCPUWaitMicroseconds\nFROM system.metric_log\nWHERE event_date >= toDate(1616030910) AND event_time >= toDateTime(1616030910)\nGROUP BY t\nORDER BY t",
              "refId": "A",
              "round": "0s",
              "skip_comments": true,
              "table": "metric_log",
              "tableLoading": false
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "CPU Wait Time",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "Âµs",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 69
          },
          "hiddenSeries": false,
          "id": 48,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": false,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "database": "system",
              "dateColDataType": "event_date",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "extrapolate": true,
              "format": "time_series",
              "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t",
              "intervalFactor": 1,
              "query": "SELECT\n    (intDiv(toUInt32(toDateTime(concat(substr(toString(event_time), 1, 16), ':00'))), 2) * 2) * 1000 as t,\n    avg(ProfileEvent_OSIOWaitMicroseconds) as ProfileEvent_OSIOWaitMicroseconds\nFROM $table\nWHERE $timeFilter\nGROUP BY t\nORDER BY t\n\n",
              "rawQuery": "SELECT\n    (intDiv(toUInt32(toDateTime(concat(substr(toString(event_time), 1, 16), ':00'))), 2) * 2) * 1000 as t,\n    avg(ProfileEvent_MergedRows) as ProfileEvent_MergedRows\nFROM system.metric_log\n\nWHERE event_time >= toDateTime(1616059575)\n\nGROUP BY t\n\nORDER BY t",
              "refId": "A",
              "round": "0s",
              "skip_comments": true,
              "table": "metric_log",
              "tableLoading": false
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "IO Wait Time",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "Âµs",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 77
          },
          "hiddenSeries": false,
          "id": 61,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": false,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "database": "system",
              "dateColDataType": "event_date",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "extrapolate": true,
              "format": "time_series",
              "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t",
              "intervalFactor": 1,
              "query": "SELECT\n    (intDiv(toUInt32(toDateTime(concat(substr(toString(event_time), 1, 16), ':00'))), 2) * 2) * 1000 as t,\n    avg(ProfileEvent_ZooKeeperWaitMicroseconds) as ProfileEvent_ZooKeeperWaitMicroseconds\nFROM $table\n\nWHERE $timeFilter\n\nGROUP BY t\n\nORDER BY t\n\n",
              "rawQuery": "SELECT\n    (intDiv(toUInt32(toDateTime(concat(substr(toString(event_time), 1, 16), ':00'))), 2) * 2) * 1000 as t,\n    avg(ProfileEvent_FailedQuery) ProfileEvent_FailedQuery\nFROM system.metric_log\n\nWHERE event_date >= toDate(1609221214) AND event_time >= toDateTime(1609221214)\n\nGROUP BY t\n\nORDER BY t",
              "refId": "A",
              "round": "0s",
              "skip_comments": true,
              "table": "metric_log",
              "tableLoading": false
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Zookeeper Wait Time",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "Âµs",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 77
          },
          "hiddenSeries": false,
          "id": 47,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": false,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "database": "system",
              "dateColDataType": "event_date",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "extrapolate": true,
              "format": "time_series",
              "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t",
              "intervalFactor": 2,
              "query": "SELECT\n    $timeSeries as t,\n    CurrentMetric_MemoryTracking\nFROM $table\nWHERE $timeFilter\nORDER BY t\n\n",
              "rawQuery": "SELECT\n    (intDiv(toUInt32(event_time), 2) * 2) * 1000 as t,\n    CurrentMetric_TCPConnection\nFROM system.metric_log\nWHERE event_date >= toDate(1608787421) AND event_time >= toDateTime(1608787421)\nORDER BY t",
              "refId": "A",
              "round": "0s",
              "skip_comments": true,
              "table": "metric_log",
              "tableLoading": false
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory Used",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "bytes",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 85
          },
          "hiddenSeries": false,
          "id": 34,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": false,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "database": "system",
              "dateColDataType": "event_date",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "extrapolate": true,
              "format": "time_series",
              "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t",
              "intervalFactor": 1,
              "query": "SELECT\n    (intDiv(toUInt32(toDateTime(concat(substr(toString(event_time), 1, 16), ':00'))), 2) * 2) * 1000 as t,\n    avg(ProfileEvent_InsertedBytes) as ProfileEvent_InsertedBytes\nFROM $table\n\nWHERE $timeFilter\n\nGROUP BY t\n\nORDER BY t\n\n",
              "rawQuery": "SELECT\n    (intDiv(toUInt32(toDateTime(concat(substr(toString(event_time), 1, 16), ':00'))), 2) * 2) * 1000 as t,\n    avg(ProfileEvent_InsertedBytes) as ProfileEvent_InsertedBytes\nFROM system.metric_log\n\nWHERE event_date >= toDate(1608861376) AND event_time >= toDateTime(1608861376)\n\nGROUP BY t\n\nORDER BY t",
              "refId": "A",
              "round": "0s",
              "skip_comments": true,
              "table": "metric_log",
              "tableLoading": false
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Write Bytes Per Second",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "bytes",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 85
          },
          "hiddenSeries": false,
          "id": 36,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": false,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "database": "system",
              "dateColDataType": "event_date",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "extrapolate": true,
              "format": "time_series",
              "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t",
              "intervalFactor": 1,
              "query": "SELECT\n    (intDiv(toUInt32(toDateTime(concat(substr(toString(event_time), 1, 16), ':00'))), 2) * 2) * 1000 as t,\n    avg(ProfileEvent_InsertedRows) as ProfileEvent_InsertedRows\nFROM $table\n\nWHERE $timeFilter\n\nGROUP BY t\n\nORDER BY t\n",
              "rawQuery": "SELECT\n    (intDiv(toUInt32(toDateTime(concat(substr(toString(event_time), 1, 16), ':00'))), 2) * 2) * 1000 as t,\n    avg(ProfileEvent_InsertedRows) as ProfileEvent_InsertedRows\nFROM system.metric_log\n\nWHERE event_date >= toDate(1608861133) AND event_time >= toDateTime(1608861133)\n\nGROUP BY t\n\nORDER BY t",
              "refId": "A",
              "round": "0s",
              "skip_comments": true,
              "table": "metric_log",
              "tableLoading": false
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Write Rows Per Second",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": "row/s",
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 93
          },
          "hiddenSeries": false,
          "id": 38,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": false,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "database": "system",
              "dateColDataType": "",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "extrapolate": true,
              "format": "time_series",
              "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t",
              "intervalFactor": 1,
              "query": "SELECT\n    (intDiv(toUInt32(toDateTime(concat(substr(toString(event_time), 1, 16), ':00'))), 2) * 2) * 1000 as t,\n    avg(ProfileEvent_MergedUncompressedBytes) as ProfileEvent_MergedUncompressedBytes\nFROM $table\n\nWHERE $timeFilter\n\nGROUP BY t\n\nORDER BY t\n",
              "rawQuery": "SELECT\n    database as DatabaseName,\n    table as TableName,\n    count(1) as ColumnCnt\nFROM system.columns\nWHERE database!='system'\nGROUP BY DatabaseName, TableName",
              "refId": "A",
              "round": "0s",
              "skip_comments": true,
              "table": "metric_log",
              "tableLoading": false
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Merged Uncompressed Bytes Per Second",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "bytes",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 93
          },
          "hiddenSeries": false,
          "id": 40,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": false,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "database": "system",
              "dateColDataType": "",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "extrapolate": true,
              "format": "time_series",
              "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t",
              "intervalFactor": 1,
              "query": "SELECT\n    (intDiv(toUInt32(toDateTime(concat(substr(toString(event_time), 1, 16), ':00'))), 2) * 2) * 1000 as t,\n    avg(ProfileEvent_MergedRows) as ProfileEvent_MergedRows\nFROM $table\n\nWHERE $timeFilter\n\nGROUP BY t\n\nORDER BY t\n\n",
              "rawQuery": "SELECT\n    (intDiv(toUInt32(toDateTime(concat(substr(toString(event_time), 1, 16), ':00'))), 2) * 2) * 1000 as t,\n    avg(ProfileEvent_MergedRows) as ProfileEvent_MergedRows\nFROM system.metric_log\n\nWHERE event_time >= toDateTime(1608707620)\n\nGROUP BY t\n\nORDER BY t",
              "refId": "A",
              "round": "0s",
              "skip_comments": true,
              "table": "metric_log",
              "tableLoading": false
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Merged Rows Per Second",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": "rows/s",
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 101
          },
          "hiddenSeries": false,
          "id": 42,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": false,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "database": "system",
              "dateColDataType": "event_date",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "extrapolate": true,
              "format": "time_series",
              "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t",
              "intervalFactor": 1,
              "query": "SELECT\n    $timeSeries as t,\n    CurrentMetric_HTTPConnection\nFROM $table\n\nWHERE $timeFilter\n\n",
              "rawQuery": "SELECT\n    (intDiv(toUInt32(event_time), 2) * 2) * 1000 as t,\n    CurrentMetric_TCPConnection\nFROM system.metric_log\n\nWHERE event_date >= toDate(1608021670) AND event_time >= toDateTime(1608021670)",
              "refId": "A",
              "round": "0s",
              "skip_comments": true,
              "table": "metric_log",
              "tableLoading": false
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "HTTP Connection",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 101
          },
          "hiddenSeries": false,
          "id": 43,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": false,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "database": "system",
              "dateColDataType": "event_date",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "extrapolate": true,
              "format": "time_series",
              "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t",
              "intervalFactor": 1,
              "query": "SELECT\n    $timeSeries as t,\n    CurrentMetric_TCPConnection\nFROM $table\nWHERE $timeFilter\nORDER BY t\n\n",
              "rawQuery": "SELECT\n    (intDiv(toUInt32(event_time), 2) * 2) * 1000 as t,\n    CurrentMetric_TCPConnection\nFROM system.metric_log\nWHERE event_date >= toDate(1608113382) AND event_time >= toDateTime(1608113382)\nORDER BY t",
              "refId": "A",
              "round": "0s",
              "skip_comments": true,
              "table": "metric_log",
              "tableLoading": false
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "TCP Connection",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 109
          },
          "hiddenSeries": false,
          "id": 49,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": false,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "database": "system",
              "dateColDataType": "event_date",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "extrapolate": true,
              "format": "time_series",
              "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t",
              "intervalFactor": 1,
              "query": "SELECT\n    (intDiv(toUInt32(toDateTime(concat(substr(toString(event_time), 1, 16), ':00'))), 2) * 2) * 1000 as t,\n    avg(ProfileEvent_Query) as ProfileEvent_Query\nFROM $table\n\nWHERE $timeFilter\n\nGROUP BY t\n\nORDER BY t\n\n",
              "rawQuery": "SELECT\n    (intDiv(toUInt32(toDateTime(concat(substr(toString(event_time), 1, 16), ':00'))), 2) * 2) * 1000 as t,\n    avg(ProfileEvent_Query) as ProfileEvent_Query\nFROM system.metric_log\n\nWHERE event_date >= toDate(1608707645) AND event_time >= toDateTime(1608707645)\n\nGROUP BY t\n\nORDER BY t",
              "refId": "A",
              "round": "0s",
              "skip_comments": true,
              "table": "metric_log",
              "tableLoading": false
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "QPS",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Altinity plugin for ClickHouse PROD",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 109
          },
          "hiddenSeries": false,
          "id": 59,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": false,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [
            {
              "alias": "/.*/",
              "color": "#C4162A"
            }
          ],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "database": "system",
              "dateColDataType": "event_date",
              "dateLoading": false,
              "dateTimeColDataType": "event_time",
              "dateTimeType": "DATETIME",
              "datetimeLoading": false,
              "extrapolate": true,
              "format": "time_series",
              "formattedQuery": "SELECT $timeSeries as t, count() FROM $table WHERE $timeFilter GROUP BY t ORDER BY t",
              "intervalFactor": 1,
              "query": "SELECT\n    (intDiv(toUInt32(toDateTime(concat(substr(toString(event_time), 1, 16), ':00'))), 2) * 2) * 1000 as t,\n    avg(ProfileEvent_FailedQuery) ProfileEvent_FailedQuery\nFROM $table\n\nWHERE $timeFilter\n\nGROUP BY t\n\nORDER BY t\n",
              "rawQuery": "SELECT\n    (intDiv(toUInt32(toDateTime(concat(substr(toString(event_time), 1, 16), ':00'))), 2) * 2) * 1000 as t,\n    avg(ProfileEvent_FailedQuery) ProfileEvent_FailedQuery\nFROM system.metric_log\n\nWHERE event_date >= toDate(1609221227) AND event_time >= toDateTime(1609221227)\n\nGROUP BY t\n\nORDER BY t",
              "refId": "A",
              "round": "0s",
              "skip_comments": true,
              "table": "metric_log",
              "tableLoading": false
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Failed QPS",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        }
      ],
      "refresh": false,
      "schemaVersion": 27,
      "style": "dark",
      "tags": [
        "clickhouse",
        "performance"
      ],
      "templating": {
        "list": [
          {
            "auto": true,
            "auto_count": 100,
            "auto_min": "1m",
            "current": {
              "selected": false,
              "text": "5m",
              "value": "5m"
            },
            "description": null,
            "error": null,
            "hide": 2,
            "label": null,
            "name": "interval",
            "options": [
              {
                "selected": false,
                "text": "auto",
                "value": "$__auto_interval_interval"
              },
              {
                "selected": true,
                "text": "5m",
                "value": "5m"
              }
            ],
            "query": "5m",
            "refresh": 2,
            "skipUrlSync": false,
            "type": "interval"
          },
          {
            "allValue": null,
            "current": {
              "selected": true,
              "tags": [],
              "text": "All",
              "value": [
                "$__all"
              ]
            },
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": true,
            "label": "type",
            "multi": true,
            "name": "type",
            "options": [
              {
                "selected": true,
                "text": "All",
                "value": "$__all"
              },
              {
                "selected": false,
                "text": "1",
                "value": "1"
              },
              {
                "selected": false,
                "text": "2",
                "value": "2"
              },
              {
                "selected": false,
                "text": "3",
                "value": "3"
              },
              {
                "selected": false,
                "text": "4",
                "value": "4"
              }
            ],
            "query": "1,2,3,4",
            "skipUrlSync": false,
            "type": "custom"
          },
          {
            "allValue": null,
            "current": {
              "selected": true,
              "tags": [],
              "text": "30",
              "value": "30"
            },
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": false,
            "label": "top elements",
            "multi": false,
            "name": "top",
            "options": [
              {
                "selected": false,
                "text": "5",
                "value": "5"
              },
              {
                "selected": false,
                "text": "10",
                "value": "10"
              },
              {
                "selected": false,
                "text": "15",
                "value": "15"
              },
              {
                "selected": false,
                "text": "20",
                "value": "20"
              },
              {
                "selected": false,
                "text": "25",
                "value": "25"
              },
              {
                "selected": true,
                "text": "30",
                "value": "30"
              }
            ],
            "query": "5,10,15,20,25,30",
            "skipUrlSync": false,
            "type": "custom"
          },
          {
            "allValue": "",
            "current": {
              "selected": false,
              "text": "",
              "value": ""
            },
            "datasource": "Altinity plugin for ClickHouse PROD",
            "definition": "select distinct initial_user from system.query_log where event_date >= today()-3 and event_time > toDateTime(today()-3)",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": true,
            "label": "initial user",
            "multi": true,
            "name": "user",
            "options": [],
            "query": "select distinct initial_user from system.query_log where event_date >= today()-3 and event_time > toDateTime(today()-3)",
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": "",
            "current": {
              "selected": true,
              "tags": [],
              "text": "all",
              "value": "all"
            },
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": false,
            "label": "query type",
            "multi": false,
            "name": "query_type",
            "options": [
              {
                "selected": true,
                "text": "all",
                "value": "all"
              },
              {
                "selected": false,
                "text": "select",
                "value": "select"
              },
              {
                "selected": false,
                "text": "insert",
                "value": "insert"
              }
            ],
            "query": "all,select,insert",
            "skipUrlSync": false,
            "type": "custom"
          }
        ]
      },
      "time": {
        "from": "now-7d",
        "to": "now"
      },
      "timepicker": {
        "refresh_intervals": [
          "5s",
          "10s",
          "30s",
          "1m",
          "5m",
          "15m",
          "30m",
          "1h",
          "2h",
          "1d"
        ],
        "time_options": [
          "5m",
          "15m",
          "1h",
          "6h",
          "12h",
          "24h",
          "2d",
          "7d",
          "30d"
        ]
      },
      "timezone": "browser",
      "title": "ClickHouse Performance Monitor",
      "uid": "TFajSstMk2",
      "version": 11
    }
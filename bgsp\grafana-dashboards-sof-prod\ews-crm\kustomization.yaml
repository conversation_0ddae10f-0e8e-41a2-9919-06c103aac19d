apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
- crm.yaml
- notification-service.yaml
- regulation-reporting-service.yaml
- bonuses-infrastructure-metrics.yaml
- bonuses-api-custom-metrics.yaml
- bonuses-processor-custom-metrics.yaml
- oauth-net.yaml
- bonuses-master-dashboard.yaml
- bonuses-background-services-alerting.yaml
- bonuses-background-scheduler.yaml
- game-journey-metrics.yaml
- game-journey-custom-metrics.yaml
- ews-bonuses-transactions-listener.yaml
- ews-authorisation-api.yaml
- ews-stream-mirroring-crm.yaml
- ews-platform-games-api-backoffice.yaml
- ews-bonuses-api-admin.yaml
- integrator-crm.yaml
- ews-foliatti-mx-populator.yaml
- ews-stream-mirroring-crm-evba.yaml
- ews-new-bu-orchestrator.yaml
- ews-platform-games-api-frontoffice.yaml
- ews-platform-games-workers.yaml
- ews-platform-games-pool-engine.yaml
- ews-regulatory-compliance-service.yaml
- ews-mfa-service.yaml
- ews-game-journey-worker-wbbg.yaml
- ews-promo-module.yaml
- ews-responsible-gambling-service.yaml
- ews-integrator-crm-backoffice-facade.yaml
- ews-segment-service-backoffice.yaml
- ews-segment-service-core.yaml
- ews-integrator-data-processing.yaml
- ews-crm-integration.yaml
- ews-pe-player-journey-nomenclatures.yaml
- ews-player-tournaments-api-backoffice.yaml
- ews-player-tournaments-api-frontoffice.yaml
- ews-player-tournaments-api-integration.yaml
- ews-player-tournaments-workers.yaml
- ews-gamification-player-updates-processor.yaml
- ews-gamification-stream-events-aggregator.yaml
- ews-gamification-stream-events-processor.yaml
- ews-gamification-processing-time-metrics.yaml
- ews-gamification-processing-player-activities.yaml
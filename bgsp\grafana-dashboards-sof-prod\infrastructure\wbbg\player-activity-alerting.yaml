apiVersion: v1
kind: ConfigMap
metadata:
  name: player-activity-alerting-wbbg
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: WBBG
data:
  player-activity-alerting-wbbg.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": 462,
      "links": [],
      "panels": [
        {
          "alert": {
            "alertRuleTags": {
              "severity": "critical"
            },
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    100
                  ],
                  "type": "lt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "C",
                    "5m",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "avg"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "5m",
            "frequency": "1m",
            "handler": 1,
            "name": "Player activity EGT Digital  - WBBG",
            "noDataState": "alerting",
            "notifications": [
              {
                "uid": "I9C9By5Vk"
              }
            ]
          },
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "EGT Interactive"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "10"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "NetEnt"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "11"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Amatic"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "12"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Casino Technology (direct)"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "14"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Habanero"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "2"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "EGT Digital Games"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "22"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Scientific Games Digital"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "3"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Evolution"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "36"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Playtech"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "4"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Playson"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "41"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "7Mojos"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "42"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Red Tiger"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "43"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "NetEnt"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "6"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Spribe"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "7"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Evoplay"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "8"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Pragmatic"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "46"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Pragmatic"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "9"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Endorphina"
                  }
                ]
              }
            ]
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "hiddenSeries": false,
          "id": 8,
          "legend": {
            "alignAsTable": true,
            "avg": true,
            "current": true,
            "hideZero": true,
            "max": true,
            "min": true,
            "rightSide": true,
            "show": true,
            "sideWidth": 400,
            "sort": "max",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "time_series",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  activity_time AS \"time\",\n  game_provider_id::text AS metric,\n  player_count\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time) AND game_provider_id != 2 AND game_provider_id != 1 AND game_provider_id != 0 and business_unit = 'WBBG'\nORDER BY 1,2",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            },
            {
              "format": "time_series",
              "group": [],
              "hide": false,
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  activity_time AS \"time\",\n  sum(player_count) AS \"Total\"\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time) AND game_provider_id != 0 and business_unit = 'WBBG'\nGROUP BY 1\nORDER BY 1",
              "refId": "B",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            },
            {
              "format": "time_series",
              "group": [],
              "hide": false,
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  activity_time AS \"time\",\n  game_provider_id::text AS metric,\n  player_count\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time) AND game_provider_id = 2 and business_unit = 'WBBG'\nORDER BY 1,2",
              "refId": "C",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            },
            {
              "format": "time_series",
              "group": [],
              "hide": false,
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  activity_time AS \"time\",\n  game_provider_id::text AS metric,\n  player_count\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time) AND game_provider_id = 1 and business_unit = 'WBBG'\nORDER BY 1,2",
              "refId": "D",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "lt",
              "value": 100,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Unique users with bets per minute, CASINO",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:515",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:516",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "alert": {
            "alertRuleTags": {
              "severity": "critical"
            },
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    100
                  ],
                  "type": "lt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "D",
                    "5m",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "avg"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "5m",
            "frequency": "1m",
            "handler": 1,
            "name": "Player activity EGT Interactive - WBBG",
            "noDataState": "alerting",
            "notifications": [
              {
                "uid": "I9C9By5Vk"
              }
            ]
          },
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "EGT Interactive"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "10"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "NetEnt"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "11"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Amatic"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "12"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Casino Technology (direct)"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "14"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Habanero"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "2"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "EGT Digital Games"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "22"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Scientific Games Digital"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "3"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Evolution"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "36"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Playtech"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "4"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Playson"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "41"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "7Mojos"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "42"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Red Tiger"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "43"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "NetEnt"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "6"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Spribe"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "7"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Evoplay"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "8"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Pragmatic"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "46"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Pragmatic"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "9"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Endorphina"
                  }
                ]
              }
            ]
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 24,
            "x": 0,
            "y": 8
          },
          "hiddenSeries": false,
          "id": 7,
          "legend": {
            "alignAsTable": true,
            "avg": true,
            "current": true,
            "hideZero": true,
            "max": true,
            "min": true,
            "rightSide": true,
            "show": true,
            "sideWidth": 400,
            "sort": "max",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "time_series",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  activity_time AS \"time\",\n  game_provider_id::text AS metric,\n  player_count\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time) AND game_provider_id != 2 AND game_provider_id != 1 AND game_provider_id != 0 and business_unit = 'WBBG'\nORDER BY 1,2",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            },
            {
              "format": "time_series",
              "group": [],
              "hide": false,
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  activity_time AS \"time\",\n  sum(player_count) AS \"Total\"\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time) AND game_provider_id != 0 and business_unit = 'WBBG'\nGROUP BY 1\nORDER BY 1",
              "refId": "B",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            },
            {
              "format": "time_series",
              "group": [],
              "hide": false,
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  activity_time AS \"time\",\n  game_provider_id::text AS metric,\n  player_count\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time) AND game_provider_id = 2 and business_unit = 'WBBG'\nORDER BY 1,2",
              "refId": "C",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            },
            {
              "format": "time_series",
              "group": [],
              "hide": false,
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  activity_time AS \"time\",\n  game_provider_id::text AS metric,\n  player_count\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time) AND game_provider_id = 1 and business_unit = 'WBBG'\nORDER BY 1,2",
              "refId": "D",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "lt",
              "value": 100,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Unique users with bets per minute, CASINO",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:515",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:516",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "alert": {
            "alertRuleTags": {
              "severity": "critical"
            },
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    1
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "1m",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "sum"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "3m",
            "frequency": "1m",
            "handler": 1,
            "name": "Alerting for player activity",
            "noDataState": "alerting",
            "notifications": [
              {
                "uid": "I9C9By5Vk"
              }
            ]
          },
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "thresholds": {
                "mode": "absolute",
                "steps": []
              },
              "unit": "short"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 7,
            "w": 24,
            "x": 0,
            "y": 16
          },
          "hiddenSeries": false,
          "id": 2,
          "legend": {
            "alignAsTable": true,
            "avg": true,
            "current": true,
            "max": true,
            "min": true,
            "rightSide": true,
            "show": true,
            "total": true,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "time_series",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT distinct\n(\n    select EXTRACT\n    (minute from\n        (\n          select distinct now()\n          -\n          (\n          SELECT max(activity_time) as max_date FROM public.player_activity\n          ) from public.player_activity\n        )\n    )\n)::numeric, \nnow() as time from public.player_activity;",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "gt",
              "value": 1,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Alerting for player activity",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:64",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:65",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "alert": {
            "alertRuleTags": {
              "severity": "critical"
            },
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    1
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "1m",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "sum"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "30m",
            "frequency": "1m",
            "handler": 1,
            "name": "Alerting for player registrations",
            "noDataState": "alerting",
            "notifications": [
              {
                "uid": "I9C9By5Vk"
              }
            ]
          },
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "thresholds": {
                "mode": "absolute",
                "steps": []
              },
              "unit": "short"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 7,
            "w": 24,
            "x": 0,
            "y": 23
          },
          "hiddenSeries": false,
          "id": 3,
          "legend": {
            "alignAsTable": true,
            "avg": true,
            "current": true,
            "max": true,
            "min": true,
            "rightSide": true,
            "show": true,
            "total": true,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "time_series",
              "group": [],
              "hide": false,
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT distinct\n(\n    select EXTRACT\n    (minute from\n        (\n          select distinct now()\n          -\n          (\n          SELECT max(create_date) as max_date FROM public.monitoring_player_registrations\n          ) from public.monitoring_player_registrations\n        )\n    )\n)::numeric, \nnow() as time from public.monitoring_player_registrations;",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "gt",
              "value": 1,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Alerting for player registrations",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:64",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:65",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        }
      ],
      "refresh": "1m",
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": []
      },
      "time": {
        "from": "now-7d",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "Alerting for player activity",
      "uid": "H4dm6wW4k",
      "version": 1
    }
apiVersion: v1
kind: ConfigMap
metadata:
  name: platform-stats
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: infrastructure
data:
  platform-stats.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": {
              "type": "grafana",
              "uid": "-- Grafana --"
            },
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "target": {
              "limit": 100,
              "matchAny": false,
              "tags": [],
              "type": "dashboard"
            },
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "fiscalYearStartMonth": 0,
      "graphTooltip": 0,
      "links": [],
      "liveNow": false,
      "panels": [
        {
          "datasource": {
            "type": "elasticsearch",
            "uid": "ACSgiYd4z"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 13,
            "w": 12,
            "x": 0,
            "y": 0
          },
          "id": 8,
          "options": {
            "basemap": {
              "config": {},
              "name": "Layer 0",
              "type": "default"
            },
            "controls": {
              "mouseWheelZoom": true,
              "showAttribution": true,
              "showDebug": false,
              "showMeasure": false,
              "showScale": false,
              "showZoom": true
            },
            "layers": [
              {
                "config": {
                  "showLegend": true,
                  "style": {
                    "color": {
                      "fixed": "dark-green"
                    },
                    "opacity": 0.4,
                    "rotation": {
                      "fixed": 0,
                      "max": 360,
                      "min": -360,
                      "mode": "mod"
                    },
                    "size": {
                      "field": "Count",
                      "fixed": 7,
                      "max": 15,
                      "min": 2
                    },
                    "symbol": {
                      "fixed": "img/icons/marker/circle.svg",
                      "mode": "fixed"
                    },
                    "text": {
                      "field": "Count",
                      "fixed": "",
                      "mode": "fixed"
                    },
                    "textConfig": {
                      "fontSize": 12,
                      "offsetX": 0,
                      "offsetY": 0,
                      "textAlign": "center",
                      "textBaseline": "middle"
                    }
                  }
                },
                "filterData": {
                  "id": "byRefId",
                  "options": "A"
                },
                "location": {
                  "geohash": "geoip_cf_connecting_ip.location",
                  "mode": "geohash"
                },
                "name": "Layer 1",
                "tooltip": true,
                "type": "markers"
              }
            ],
            "tooltip": {
              "mode": "details"
            },
            "view": {
              "allLayers": true,
              "id": "europe",
              "lat": 46,
              "lon": 14,
              "zoom": 4
            }
          },
          "pluginVersion": "9.2.5",
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "field": "geoip_cf_connecting_ip.location",
                  "id": "7",
                  "settings": {
                    "precision": "7"
                  },
                  "type": "geohash_grid"
                }
              ],
              "datasource": {
                "type": "elasticsearch",
                "uid": "ACSgiYd4z"
              },
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "http_host: \"$BU\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "map",
          "type": "geomap"
        },
        {
          "datasource": {
            "type": "elasticsearch",
            "uid": "ACSgiYd4z"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineStyle": {
                  "fill": "solid"
                },
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "auto",
                "spanNulls": true,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "s"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 0
          },
          "id": 2,
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "single",
              "sort": "none"
            }
          },
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "datasource": {
                "type": "elasticsearch",
                "uid": "ACSgiYd4z"
              },
              "metrics": [
                {
                  "field": "request_time",
                  "id": "1",
                  "settings": {
                    "percents": [
                      "25",
                      "50",
                      "75",
                      "95",
                      "99"
                    ]
                  },
                  "type": "percentiles"
                }
              ],
              "query": "http_host: \"$BU\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "Percentiles response time",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "elasticsearch",
            "uid": "ACSgiYd4z"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "auto",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 9
          },
          "id": 6,
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "single",
              "sort": "none"
            }
          },
          "pluginVersion": "9.2.5",
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "field": "http_cf_connecting_ip.keyword",
                  "id": "2",
                  "settings": {
                    "min_doc_count": "1",
                    "order": "desc",
                    "orderBy": "3",
                    "size": "15"
                  },
                  "type": "terms"
                },
                {
                  "field": "@timestamp",
                  "id": "4",
                  "settings": {
                    "interval": "auto",
                    "min_doc_count": "0",
                    "timeZone": "utc",
                    "trimEdges": "0"
                  },
                  "type": "date_histogram"
                }
              ],
              "datasource": {
                "type": "elasticsearch",
                "uid": "ACSgiYd4z"
              },
              "metrics": [
                {
                  "id": "3",
                  "type": "count"
                }
              ],
              "query": "path:\"/oauth/token\" AND method: \"POST\" AND http_host: \"$BU\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "Login per ip",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "elasticsearch",
            "uid": "ACSgiYd4z"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "auto",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 13
          },
          "id": 16,
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "single",
              "sort": "none"
            }
          },
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "field": "path.keyword",
                  "id": "3",
                  "settings": {
                    "min_doc_count": "1",
                    "order": "desc",
                    "orderBy": "_count",
                    "size": "20"
                  },
                  "type": "terms"
                },
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "datasource": {
                "type": "elasticsearch",
                "uid": "ACSgiYd4z"
              },
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "http_host: \"$BU\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "Top Path",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "elasticsearch",
            "uid": "ACSgiYd4z"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "auto",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 17
          },
          "id": 23,
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "single",
              "sort": "none"
            }
          },
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "field": "path.keyword",
                  "id": "3",
                  "settings": {
                    "min_doc_count": "1",
                    "order": "desc",
                    "orderBy": "1",
                    "size": "5"
                  },
                  "type": "terms"
                },
                {
                  "field": "http_cf_connecting_ip.keyword",
                  "id": "4",
                  "settings": {
                    "min_doc_count": "1",
                    "order": "desc",
                    "orderBy": "1",
                    "size": "5"
                  },
                  "type": "terms"
                },
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "1m"
                  },
                  "type": "date_histogram"
                }
              ],
              "datasource": {
                "type": "elasticsearch",
                "uid": "ACSgiYd4z"
              },
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "path: *validation* AND http_host: \"$BU\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "Validation request per 5 ip",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "elasticsearch",
            "uid": "ACSgiYd4z"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "auto",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 21
          },
          "id": 12,
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "single",
              "sort": "none"
            }
          },
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "field": "status.keyword",
                  "id": "3",
                  "settings": {
                    "min_doc_count": "1",
                    "order": "desc",
                    "orderBy": "_term",
                    "size": "20"
                  },
                  "type": "terms"
                },
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "datasource": {
                "type": "elasticsearch",
                "uid": "ACSgiYd4z"
              },
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "http_host: \"$BU\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "Status",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "elasticsearch",
            "uid": "ACSgiYd4z"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "auto",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "decimals": 0,
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 25
          },
          "id": 10,
          "links": [],
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "single",
              "sort": "none"
            }
          },
          "pluginVersion": "9.2.5",
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "field": "method.keyword",
                  "id": "4",
                  "settings": {
                    "order": "desc",
                    "orderBy": "_count",
                    "size": "10"
                  },
                  "type": "terms"
                },
                {
                  "field": "@timestamp",
                  "id": "5",
                  "settings": {
                    "interval": "auto",
                    "min_doc_count": "0",
                    "timeZone": "utc",
                    "trimEdges": "0"
                  },
                  "type": "date_histogram"
                }
              ],
              "datasource": {
                "type": "elasticsearch",
                "uid": "ACSgiYd4z"
              },
              "hide": false,
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "http_host: \"$BU\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "Method",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "elasticsearch",
            "uid": "ACSgiYd4z"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                }
              },
              "mappings": []
            },
            "overrides": []
          },
          "gridPos": {
            "h": 7,
            "w": 4,
            "x": 0,
            "y": 29
          },
          "id": 14,
          "links": [],
          "options": {
            "displayLabels": [
              "percent",
              "name"
            ],
            "legend": {
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": true
            },
            "pieType": "pie",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "tooltip": {
              "mode": "single",
              "sort": "none"
            }
          },
          "pluginVersion": "9.2.5",
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "field": "upstream_cache_status.keyword",
                  "id": "2",
                  "settings": {
                    "min_doc_count": "1",
                    "order": "desc",
                    "orderBy": "_term",
                    "size": "10"
                  },
                  "type": "terms"
                },
                {
                  "field": "@timestamp",
                  "id": "3",
                  "settings": {
                    "interval": "5m",
                    "min_doc_count": "0",
                    "timeZone": "utc",
                    "trimEdges": "0"
                  },
                  "type": "date_histogram"
                }
              ],
              "datasource": {
                "type": "elasticsearch",
                "uid": "ACSgiYd4z"
              },
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "http_host: \"$BU\" AND NOT upstream_cache_status.keyword: \"\"e",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "Upstream Status (ignore empty)",
          "type": "piechart"
        },
        {
          "datasource": {
            "type": "elasticsearch",
            "uid": "ACSgiYd4z"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {
                "align": "auto",
                "displayMode": "auto",
                "inspect": false
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 17,
            "w": 5,
            "x": 4,
            "y": 29
          },
          "id": 5,
          "options": {
            "footer": {
              "fields": "",
              "reducer": [
                "sum"
              ],
              "show": false
            },
            "showHeader": true
          },
          "pluginVersion": "9.2.5",
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "field": "geoip_cf_connecting_ip.country_code2.keyword",
                  "id": "2",
                  "settings": {
                    "min_doc_count": "1",
                    "order": "desc",
                    "orderBy": "3",
                    "size": "15"
                  },
                  "type": "terms"
                }
              ],
              "datasource": {
                "type": "elasticsearch",
                "uid": "ACSgiYd4z"
              },
              "metrics": [
                {
                  "id": "3",
                  "type": "count"
                }
              ],
              "query": "http_host: \"$BU\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "Top Country",
          "type": "table"
        },
        {
          "datasource": {
            "type": "elasticsearch",
            "uid": "ACSgiYd4z"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {
                "align": "auto",
                "displayMode": "auto",
                "inspect": false
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 17,
            "w": 3,
            "x": 9,
            "y": 29
          },
          "id": 4,
          "options": {
            "footer": {
              "fields": "",
              "reducer": [
                "sum"
              ],
              "show": false
            },
            "showHeader": true
          },
          "pluginVersion": "9.2.5",
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "field": "http_cf_connecting_ip.keyword",
                  "id": "2",
                  "settings": {
                    "min_doc_count": "1",
                    "order": "desc",
                    "orderBy": "3",
                    "size": "15"
                  },
                  "type": "terms"
                }
              ],
              "datasource": {
                "type": "elasticsearch",
                "uid": "ACSgiYd4z"
              },
              "metrics": [
                {
                  "id": "3",
                  "type": "count"
                }
              ],
              "query": "path:\"/oauth/token\" AND method: \"POST\" AND http_host: \"$BU\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "Login per ip",
          "type": "table"
        },
        {
          "datasource": {
            "type": "elasticsearch",
            "uid": "ACSgiYd4z"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisCenteredZero": false,
                "axisColorMode": "text",
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 0,
                "gradientMode": "none",
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "auto",
                "spanNulls": false,
                "stacking": {
                  "group": "A",
                  "mode": "none"
                },
                "thresholdsStyle": {
                  "mode": "off"
                }
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 33
          },
          "id": 18,
          "options": {
            "legend": {
              "calcs": [],
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "single",
              "sort": "none"
            }
          },
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "field": "path.keyword",
                  "id": "4",
                  "settings": {
                    "min_doc_count": "1",
                    "order": "desc",
                    "orderBy": "1",
                    "size": "10"
                  },
                  "type": "terms"
                },
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "datasource": {
                "type": "elasticsearch",
                "uid": "ACSgiYd4z"
              },
              "metrics": [
                {
                  "field": "request_time",
                  "id": "1",
                  "type": "avg"
                }
              ],
              "query": "http_host: \"$BU\" AND request_time:>5",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "Over 5 second request",
          "type": "timeseries"
        },
        {
          "datasource": {
            "type": "elasticsearch",
            "uid": "ACSgiYd4z"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 6,
            "w": 4,
            "x": 0,
            "y": 36
          },
          "id": 25,
          "options": {
            "orientation": "auto",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "showThresholdLabels": false,
            "showThresholdMarkers": true
          },
          "pluginVersion": "9.2.5",
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "5m",
                    "min_doc_count": "0"
                  },
                  "type": "date_histogram"
                }
              ],
              "datasource": {
                "type": "elasticsearch",
                "uid": "ACSgiYd4z"
              },
              "metrics": [
                {
                  "field": "kubernetes.pod_name.keyword",
                  "id": "3",
                  "type": "cardinality"
                }
              ],
              "query": "",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "Nginx Pod Count",
          "type": "gauge"
        },
        {
          "datasource": {
            "type": "elasticsearch",
            "uid": "ACSgiYd4z"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                }
              },
              "mappings": []
            },
            "overrides": []
          },
          "gridPos": {
            "h": 13,
            "w": 4,
            "x": 12,
            "y": 41
          },
          "id": 20,
          "options": {
            "displayLabels": [
              "percent",
              "name"
            ],
            "legend": {
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": true,
              "values": [
                "percent"
              ]
            },
            "pieType": "pie",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "tooltip": {
              "mode": "single",
              "sort": "none"
            }
          },
          "pluginVersion": "9.2.5",
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "field": "user_agent.os_name.keyword",
                  "id": "3",
                  "settings": {
                    "min_doc_count": "1",
                    "order": "desc",
                    "orderBy": "1",
                    "size": "20"
                  },
                  "type": "terms"
                },
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "5m",
                    "min_doc_count": "0",
                    "trimEdges": "0"
                  },
                  "type": "date_histogram"
                }
              ],
              "datasource": {
                "type": "elasticsearch",
                "uid": "ACSgiYd4z"
              },
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "http_host: \"$BU\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "user agent os name",
          "type": "piechart"
        },
        {
          "datasource": {
            "type": "elasticsearch",
            "uid": "ACSgiYd4z"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                }
              },
              "mappings": []
            },
            "overrides": []
          },
          "gridPos": {
            "h": 13,
            "w": 4,
            "x": 16,
            "y": 41
          },
          "id": 21,
          "options": {
            "displayLabels": [
              "percent",
              "name"
            ],
            "legend": {
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": true,
              "values": [
                "percent"
              ]
            },
            "pieType": "pie",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "tooltip": {
              "mode": "single",
              "sort": "none"
            }
          },
          "pluginVersion": "9.2.5",
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "field": "user_agent.name.keyword",
                  "id": "3",
                  "settings": {
                    "min_doc_count": "1",
                    "order": "desc",
                    "orderBy": "1",
                    "size": "20"
                  },
                  "type": "terms"
                },
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "5m",
                    "min_doc_count": "0",
                    "trimEdges": "0"
                  },
                  "type": "date_histogram"
                }
              ],
              "datasource": {
                "type": "elasticsearch",
                "uid": "ACSgiYd4z"
              },
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "http_host: \"$BU\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "user agent name",
          "type": "piechart"
        },
        {
          "datasource": {
            "type": "elasticsearch",
            "uid": "ACSgiYd4z"
          },
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "hideFrom": {
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                }
              },
              "mappings": []
            },
            "overrides": []
          },
          "gridPos": {
            "h": 13,
            "w": 4,
            "x": 20,
            "y": 41
          },
          "id": 26,
          "options": {
            "displayLabels": [
              "percent",
              "name"
            ],
            "legend": {
              "displayMode": "list",
              "placement": "bottom",
              "showLegend": true,
              "values": [
                "percent"
              ]
            },
            "pieType": "pie",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "tooltip": {
              "mode": "single",
              "sort": "none"
            }
          },
          "pluginVersion": "9.2.5",
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "field": "http_referer.keyword",
                  "id": "3",
                  "settings": {
                    "min_doc_count": "1",
                    "order": "desc",
                    "orderBy": "1",
                    "size": "10"
                  },
                  "type": "terms"
                },
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "5m",
                    "min_doc_count": "0",
                    "trimEdges": "0"
                  },
                  "type": "date_histogram"
                }
              ],
              "datasource": {
                "type": "elasticsearch",
                "uid": "ACSgiYd4z"
              },
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "http_host: \"$BU\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "title": "referer",
          "type": "piechart"
        }
      ],
      "schemaVersion": 37,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": [
          {
            "current": {
              "selected": false,
              "text": "winbet-api",
              "value": "winbet-api.egt-digital.com"
            },
            "hide": 0,
            "includeAll": false,
            "label": "business unit",
            "multi": false,
            "name": "BU",
            "options": [
              {
                "selected": true,
                "text": "winbet-api",
                "value": "winbet-api.egt-digital.com"
              },
              {
                "selected": false,
                "text": "inbet-api",
                "value": "inbet-api.egt-digital.com"
              },
              {
                "selected": false,
                "text": "winbet-ro-api",
                "value": "winbet-ro-api.egt-digital.com"
              },
              {
                "selected": false,
                "text": "sesame-api",
                "value": "sesame-api.egt-digital.com"
              }
            ],
            "query": "winbet-api : winbet-api.egt-digital.com, inbet-api : inbet-api.egt-digital.com, winbet-ro-api : winbet-ro-api.egt-digital.com, sesame-api : sesame-api.egt-digital.com",
            "queryValue": "",
            "skipUrlSync": false,
            "type": "custom"
          }
        ]
      },
      "time": {
        "from": "now-15m",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "Platform Stats",
      "uid": "HMe4MLdVz",
      "version": 1,
      "weekStart": ""
    }
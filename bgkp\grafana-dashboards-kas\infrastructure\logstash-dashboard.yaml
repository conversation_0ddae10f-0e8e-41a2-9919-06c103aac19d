apiVersion: v1
kind: ConfigMap
metadata:
  name: logstash-dashboard
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: logstash-dashboard
data:
  logstash.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "limit": 100,
            "name": "Annotations & Alerts",
            "showIn": 0,
            "type": "dashboard"
          }
        ]
      },
      "description": "System, JVM, Pipeline and individual plugin stats",
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": 26,
      "iteration": 1641991345010,
      "links": [],
      "panels": [
        {
          "collapsed": false,
          "datasource": "Prometheus",
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "id": 2459,
          "panels": [],
          "repeat": null,
          "title": "System",
          "type": "row"
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Prometheus",
          "description": "",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 7,
            "w": 12,
            "x": 0,
            "y": 1
          },
          "hiddenSeries": false,
          "hideTimeOverride": false,
          "id": 5934,
          "interval": "",
          "legend": {
            "alignAsTable": false,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": false,
            "show": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null as zero",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 1,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "irate(logstash_pipeline_events_in{instance=~'$instance'}[$__range])",
              "instant": false,
              "legendFormat": "{{instance_pqdn}}",
              "refId": "A"
            },
            {
              "expr": "sum by (job) (irate(logstash_pipeline_events_in{instance=~'$instance'}[$__range]))",
              "legendFormat": "total",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": "1h",
          "timeRegions": [],
          "timeShift": null,
          "title": "Pipeline input events / sec over the last hour",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Prometheus",
          "description": "",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 7,
            "w": 12,
            "x": 12,
            "y": 1
          },
          "hiddenSeries": false,
          "hideTimeOverride": false,
          "id": 6098,
          "interval": "",
          "legend": {
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "show": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null as zero",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 1,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "irate(logstash_pipeline_plugins_outputs_events_out{instance=~'$instance'}[$__range])",
              "instant": false,
              "legendFormat": "{{instance_pqdn}}",
              "refId": "A"
            },
            {
              "expr": "sum by (job) (irate(logstash_pipeline_plugins_outputs_events_out{instance=~'$instance'}[$__range]))",
              "legendFormat": "total",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": "1h",
          "timeRegions": [],
          "timeShift": null,
          "title": "Pipeline output events / sec over the last hour",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "collapsed": true,
          "datasource": "Prometheus",
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 8
          },
          "id": 953,
          "panels": [
            {
              "aliasColors": {},
              "bars": false,
              "cacheTimeout": null,
              "dashLength": 10,
              "dashes": false,
              "datasource": "Prometheus",
              "fieldConfig": {
                "defaults": {
                  "custom": {},
                  "links": []
                },
                "overrides": []
              },
              "fill": 1,
              "fillGradient": 0,
              "gridPos": {
                "h": 7,
                "w": 12,
                "x": 0,
                "y": 9
              },
              "hiddenSeries": false,
              "id": 1599,
              "legend": {
                "avg": false,
                "current": true,
                "max": true,
                "min": false,
                "show": true,
                "total": false,
                "values": true
              },
              "lines": true,
              "linewidth": 1,
              "links": [],
              "nullPointMode": "null",
              "options": {
                "alertThreshold": true
              },
              "percentage": false,
              "pluginVersion": "7.4.2",
              "pointradius": 2,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "logstash_jvm_mem_heap_used_percent{instance=~'$instance'}",
                  "legendFormat": "{{instance_pqdn}}-jvm-usage",
                  "refId": "A"
                }
              ],
              "thresholds": [],
              "timeFrom": null,
              "timeRegions": [],
              "timeShift": null,
              "title": "JVM Heap % used",
              "tooltip": {
                "shared": true,
                "sort": 0,
                "value_type": "individual"
              },
              "transparent": true,
              "type": "graph",
              "xaxis": {
                "buckets": null,
                "mode": "time",
                "name": null,
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "percent",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": null,
                  "show": true
                },
                {
                  "format": "short",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": null,
                  "show": true
                }
              ],
              "yaxis": {
                "align": false,
                "alignLevel": null
              }
            },
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "Prometheus",
              "fieldConfig": {
                "defaults": {
                  "custom": {},
                  "links": []
                },
                "overrides": []
              },
              "fill": 0,
              "fillGradient": 0,
              "gridPos": {
                "h": 7,
                "w": 12,
                "x": 12,
                "y": 9
              },
              "hiddenSeries": false,
              "id": 1435,
              "legend": {
                "avg": false,
                "current": true,
                "max": false,
                "min": false,
                "show": true,
                "total": false,
                "values": true
              },
              "lines": true,
              "linewidth": 1,
              "nullPointMode": "null",
              "options": {
                "alertThreshold": true
              },
              "percentage": false,
              "pluginVersion": "7.4.2",
              "pointradius": 2,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "logstash_jvm_mem_heap_max_in_bytes{instance=~'$instance'}",
                  "legendFormat": "{{instance_pqdn}}-jvm-max",
                  "refId": "A"
                },
                {
                  "expr": "logstash_jvm_mem_heap_used_in_bytes{instance=~'$instance'}",
                  "legendFormat": "{{instance_pqdn}}-jvm-used",
                  "refId": "B"
                }
              ],
              "thresholds": [],
              "timeFrom": null,
              "timeRegions": [],
              "timeShift": null,
              "title": "JVM Heap usage",
              "tooltip": {
                "shared": true,
                "sort": 0,
                "value_type": "individual"
              },
              "transparent": true,
              "type": "graph",
              "xaxis": {
                "buckets": null,
                "mode": "time",
                "name": null,
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "bytes",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": null,
                  "show": true
                },
                {
                  "format": "short",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": null,
                  "show": true
                }
              ],
              "yaxis": {
                "align": false,
                "alignLevel": null
              }
            },
            {
              "cacheTimeout": null,
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "#299c46",
                "rgba(237, 129, 40, 0.89)",
                "#d44a3a"
              ],
              "datasource": "Prometheus",
              "fieldConfig": {
                "defaults": {
                  "custom": {}
                },
                "overrides": []
              },
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "gridPos": {
                "h": 3,
                "w": 24,
                "x": 0,
                "y": 16
              },
              "id": 1733,
              "interval": null,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "nullText": null,
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "repeat": "instance",
              "repeatDirection": "h",
              "scopedVars": {
                "instance": {
                  "selected": true,
                  "text": "172.17.50.230:9304",
                  "value": "172.17.50.230:9304"
                }
              },
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false,
                "ymax": null,
                "ymin": null
              },
              "tableColumn": "",
              "targets": [
                {
                  "expr": "logstash_jvm_gc_collectors_old_collection_count{instance=~'$instance'}",
                  "refId": "A"
                }
              ],
              "thresholds": "",
              "timeFrom": null,
              "timeShift": null,
              "title": "$instance - GC old events",
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "current"
            },
            {
              "cacheTimeout": null,
              "colorBackground": false,
              "colorValue": false,
              "colors": [
                "#299c46",
                "rgba(237, 129, 40, 0.89)",
                "#d44a3a"
              ],
              "datasource": "Prometheus",
              "description": "Returns the rounded number of GC young events as measured over the time period specified by the user.",
              "fieldConfig": {
                "defaults": {
                  "custom": {}
                },
                "overrides": []
              },
              "format": "none",
              "gauge": {
                "maxValue": 100,
                "minValue": 0,
                "show": false,
                "thresholdLabels": false,
                "thresholdMarkers": true
              },
              "gridPos": {
                "h": 3,
                "w": 24,
                "x": 0,
                "y": 19
              },
              "id": 1804,
              "interval": null,
              "links": [],
              "mappingType": 1,
              "mappingTypes": [
                {
                  "name": "value to text",
                  "value": 1
                },
                {
                  "name": "range to text",
                  "value": 2
                }
              ],
              "maxDataPoints": 100,
              "nullPointMode": "connected",
              "nullText": null,
              "postfix": "",
              "postfixFontSize": "50%",
              "prefix": "",
              "prefixFontSize": "50%",
              "rangeMaps": [
                {
                  "from": "null",
                  "text": "N/A",
                  "to": "null"
                }
              ],
              "repeat": "instance",
              "repeatDirection": "h",
              "scopedVars": {
                "instance": {
                  "selected": true,
                  "text": "172.17.50.230:9304",
                  "value": "172.17.50.230:9304"
                }
              },
              "sparkline": {
                "fillColor": "rgba(31, 118, 189, 0.18)",
                "full": false,
                "lineColor": "rgb(31, 120, 193)",
                "show": false,
                "ymax": null,
                "ymin": null
              },
              "tableColumn": "",
              "targets": [
                {
                  "expr": "ceil(increase(logstash_jvm_gc_collectors_young_collection_count{instance=~'$instance'}[$__range]))",
                  "refId": "A"
                }
              ],
              "thresholds": "",
              "timeFrom": null,
              "timeShift": null,
              "title": "$instance - GC young events",
              "type": "singlestat",
              "valueFontSize": "80%",
              "valueMaps": [
                {
                  "op": "=",
                  "text": "N/A",
                  "value": "null"
                }
              ],
              "valueName": "current"
            }
          ],
          "repeat": null,
          "title": "JVM",
          "type": "row"
        },
        {
          "collapsed": false,
          "datasource": "Prometheus",
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 9
          },
          "id": 2639,
          "panels": [],
          "repeat": null,
          "title": "Pipeline",
          "type": "row"
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Prometheus",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 6,
            "w": 12,
            "x": 0,
            "y": 10
          },
          "hiddenSeries": false,
          "id": 2641,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": false,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "logstash_pipeline_events_queue_push_duration_in_millis{instance=~'$instance'}/logstash_pipeline_events_in{instance=~'$instance'}",
              "legendFormat": "{{instance_pqdn}} - avg waiting time",
              "refId": "A"
            },
            {
              "expr": "logstash_pipeline_events_duration_in_millis{instance=~'$instance'}/logstash_pipeline_events_out{instance=~'$instance'}",
              "legendFormat": "{{instance_pqdn}} - avg process time",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Events processing times",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "ms",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Prometheus",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 6,
            "w": 12,
            "x": 12,
            "y": 10
          },
          "hiddenSeries": false,
          "id": 2817,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": false,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "logstash_pipeline_events_out{instance=~'***********:9304'}/(logstash_pipeline_events_duration_in_millis{instance=~'***********:9304'}/1000)",
              "interval": "",
              "legendFormat": "{{instance_pqdn}} - processed events/sec",
              "refId": "A"
            },
            {
              "expr": "logstash_pipeline_events_in{instance=~'***********:9304'}/(logstash_pipeline_events_queue_push_duration_in_millis{instance=~'***********:9304'}/1000)",
              "interval": "",
              "legendFormat": "{{instance_pqdn}} - pushed events/sec",
              "refId": "B"
            },
            {
              "expr": "logstash_pipeline_plugins_outputs_events_out{instance='$instance',pipeline=\"main\",id=~'$output_plugin'}/(logstash_pipeline_plugins_outputs_events_duration_in_millis{instance='$instance',pipeline=\"main\",id=~'$output_plugin'}/1000)",
              "legendFormat": "{{instance_pqdn}} - processed events/sec in output plugin",
              "refId": "C"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Processed events (I/O)/ sec",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Prometheus",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 6,
            "w": 24,
            "x": 0,
            "y": 16
          },
          "hiddenSeries": false,
          "id": 6745,
          "interval": "",
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "increase(logstash_pipeline_plugins_outputs_events_out{instance=~'$instance',id=~'$output_plugin'}[$__range])",
              "legendFormat": "{{instance_pqdn}}",
              "refId": "A"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Output events",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Prometheus",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 6,
            "w": 12,
            "x": 0,
            "y": 22
          },
          "hiddenSeries": false,
          "id": 1115,
          "legend": {
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "show": false,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "logstash_jvm_gc_collectors_old_collection_time_in_millis{instance=~'$instance'}/logstash_jvm_gc_collectors_old_collection_count{instance=~'$instance'}",
              "legendFormat": "{{instance_pqdn}}-gc-old",
              "refId": "A"
            },
            {
              "expr": "logstash_jvm_gc_collectors_young_collection_time_in_millis{instance=~'$instance'}/logstash_jvm_gc_collectors_young_collection_count{instance=~'$instance'}",
              "legendFormat": "{{instance_pqdn}}-gc-young",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Average time spent for GC (yound & old gens in ms)",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "ms",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Prometheus",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 6,
            "w": 12,
            "x": 12,
            "y": 22
          },
          "hiddenSeries": false,
          "id": 2127,
          "legend": {
            "alignAsTable": false,
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": false,
            "show": false,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "logstash_jvm_threads_count{instance=~'$instance'}",
              "legendFormat": "{{instance_pqdn}}-threads_count",
              "refId": "A"
            },
            {
              "expr": "logstash_jvm_threads_peak_count{instance=~'$instance'}",
              "legendFormat": "{{instance_pqdn}}-threads_peak",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Threads count",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        }
      ],
      "refresh": "",
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": [
          {
            "allValue": null,
            "current": {
              "selected": false,
              "text": "All",
              "value": "$__all"
            },
            "datasource": "Prometheus",
            "definition": "query_result({job='logstash-exporter'})",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": true,
            "label": null,
            "multi": true,
            "name": "instance",
            "options": [],
            "query": {
              "query": "query_result({job='logstash-exporter'})",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "/.*instance=\"([^\"]*).*/",
            "skipUrlSync": false,
            "sort": 1,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": "",
            "current": {
              "selected": true,
              "text": [
                "All"
              ],
              "value": [
                "$__all"
              ]
            },
            "datasource": "Prometheus",
            "definition": "query_result(logstash_pipeline_plugins_filters_events_duration_in_millis{instance=~'$instance'})",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": true,
            "label": null,
            "multi": true,
            "name": "plugin_id",
            "options": [],
            "query": {
              "query": "query_result(logstash_pipeline_plugins_filters_events_duration_in_millis{instance=~'$instance'})",
              "refId": "Prometheus-plugin_id-Variable-Query"
            },
            "refresh": 1,
            "regex": "/.*id=\"([^\"]*).*/",
            "skipUrlSync": false,
            "sort": 2,
            "tagValuesQuery": "label_values(logstash_pipeline_plugins_filters_events_duration_in_millis{name='$tag'},id)",
            "tags": [
              {
                "selected": false,
                "text": "mutate"
              },
              {
                "selected": false,
                "text": "json"
              },
              {
                "selected": false,
                "text": "date"
              },
              {
                "selected": false,
                "text": "grok"
              }
            ],
            "tagsQuery": "label_values(logstash_pipeline_plugins_filters_events_duration_in_millis{instance=~'$instance'},name)",
            "type": "query",
            "useTags": true
          },
          {
            "allValue": null,
            "current": {
              "selected": true,
              "text": [
                "All"
              ],
              "value": [
                "$__all"
              ]
            },
            "datasource": "Prometheus",
            "definition": "query_result(logstash_pipeline_plugins_inputs_events_out{instance=~'$instance'})",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": true,
            "label": null,
            "multi": true,
            "name": "input_plugin",
            "options": [],
            "query": {
              "query": "query_result(logstash_pipeline_plugins_inputs_events_out{instance=~'$instance'})",
              "refId": "Prometheus-input_plugin-Variable-Query"
            },
            "refresh": 1,
            "regex": "/.*id=\"([^\"]*).*/",
            "skipUrlSync": false,
            "sort": 2,
            "tagValuesQuery": "label_values(logstash_pipeline_plugins_inputs_events_out{name='$tag'},id)",
            "tags": [
              {
                "selected": false,
                "text": "kafka"
              }
            ],
            "tagsQuery": "label_values(logstash_pipeline_plugins_inputs_events_out{instance=~'$instance'},name)",
            "type": "query",
            "useTags": true
          },
          {
            "allValue": null,
            "current": {
              "selected": true,
              "text": [
                "All"
              ],
              "value": [
                "$__all"
              ]
            },
            "datasource": "Prometheus",
            "definition": "query_result(logstash_pipeline_plugins_outputs_events_out{instance=~'$instance'})",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": true,
            "label": null,
            "multi": true,
            "name": "output_plugin",
            "options": [],
            "query": {
              "query": "query_result(logstash_pipeline_plugins_outputs_events_out{instance=~'$instance'})",
              "refId": "Prometheus-output_plugin-Variable-Query"
            },
            "refresh": 1,
            "regex": "/.*id=\"([^\"]*).*/",
            "skipUrlSync": false,
            "sort": 2,
            "tagValuesQuery": "label_values(logstash_pipeline_plugins_outputs_events_out{name='$tag'},id)",
            "tags": [
              {
                "selected": false,
                "text": "elasticsearch"
              }
            ],
            "tagsQuery": "label_values(logstash_pipeline_plugins_outputs_events_out{instance=~'$instance'},name)",
            "type": "query",
            "useTags": true
          }
        ]
      },
      "time": {
        "from": "now-15m",
        "to": "now"
      },
      "timepicker": {
        "refresh_intervals": [
          "5s",
          "10s",
          "30s",
          "1m",
          "5m",
          "15m",
          "30m",
          "1h",
          "2h",
          "1d"
        ]
      },
      "timezone": "",
      "title": "Logstash monitoring",
      "uid": "zZQH3zhWz",
      "version": 2
    }
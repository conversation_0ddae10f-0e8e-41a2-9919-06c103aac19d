apiVersion: v1
kind: ConfigMap
metadata:
  name: sport-offering
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: ews-sports
data:
  sport-offering.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": 641,
      "links": [],
      "panels": [
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "ms"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 7,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "hiddenSeries": false,
          "id": 2,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "time_series",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  date AS \"time\",\n  delay\nFROM sport_offering_${feed_type}timing\nWHERE\n  $__timeFilter(date)\nORDER BY 1",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "delay"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_${feed_type}timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Feed delay",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:365",
              "format": "ms",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:366",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "alert": {
            "alertRuleTags": {
              "job": "feed-delay-live"
            },
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    2000
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "30s",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "avg"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "30s",
            "frequency": "15s",
            "handler": 1,
            "message": "Live producer delay is over 2 seconds",
            "name": "Feed delay by producer id alert - LIVE",
            "noDataState": "alerting",
            "notifications": []
          },
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "ms"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "1 - Live Odds"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "3"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "3 - Betradar Ctrl"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "6"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "6 - Virtual football"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "8"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "8 - Virtual Basketball League"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "10"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "10 - Virtual Dog Racing"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "11"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "11 - Virtual Horse Classics"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "12"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "12 - Virtual Tennis In-Play"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "15"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "15 - Virtual Baseball In-Play"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "20"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "20 - Betgenius Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "21"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "21 - Betgenius Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "30"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "30 - VirtualSports"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "101"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "101 - Aggregator Live Odds"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "103"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "103 - Aggregator Betradar Ctrl"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "120"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "120 - Aggregator Betgenius Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "121"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "121 - Aggregator Betgenius Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "40"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "40 - Manual"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "150"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "150 - FA LSport Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "151"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "151 - FA LSport Live"
                  }
                ]
              }
            ]
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 7
          },
          "hiddenSeries": false,
          "id": 3,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "time_series",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  date AS \"time\",\n  delay,\n  producer_id\nFROM sport_offering_${feed_type}timing\nWHERE\n  $__timeFilter(date) and producer_id::integer IN (1, 21, 101, 121, 151)\nORDER BY 1",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "delay"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_${feed_type}timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "gt",
              "value": 2000,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Feed delay by producer id - LIVE",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "ms",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "alert": {
            "alertRuleTags": {
              "job": "feed-delay-others"
            },
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    10000
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "1m",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "avg"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "1m",
            "frequency": "30s",
            "handler": 1,
            "message": "",
            "name": "Feed delay by producer id - OTHERS alert",
            "noDataState": "alerting",
            "notifications": []
          },
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "ms"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "1 - Live Odds"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "3"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "3 - Betradar Ctrl"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "6"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "6 - Virtual football"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "8"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "8 - Virtual Basketball League"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "10"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "10 - Virtual Dog Racing"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "11"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "11 - Virtual Horse Classics"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "12"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "12 - Virtual Tennis In-Play"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "15"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "15 - Virtual Baseball In-Play"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "20"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "20 - Betgenius Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "21"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "21 - Betgenius Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "30"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "30 - VirtualSports"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "101"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "101 - Aggregator Live Odds"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "103"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "103 - Aggregator Betradar Ctrl"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "120"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "120 - Aggregator Betgenius Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "121"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "121 - Aggregator Betgenius Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "40"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "40 - Manual"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "150"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "150 - FA LSport Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "151"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "151 - FA LSport Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "501"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "501 - Premium Cricket Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "503"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "503 - Premium Cricket Prematch"
                  }
                ]
              }
            ]
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 7
          },
          "hiddenSeries": false,
          "id": 16,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "time_series",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  date AS \"time\",\n  delay,\n  producer_id\nFROM sport_offering_${feed_type}timing\nWHERE\n  $__timeFilter(date) and producer_id::integer NOT IN (1, 101, 21, 121)\nORDER BY 1",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "delay"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_${feed_type}timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "gt",
              "value": 10000,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Feed delay by producer id - OTHERS",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "ms",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "ms"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "1 - Live Odds"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "3"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "3 - Betradar Ctrl"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "6"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "6 - Virtual football"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "8"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "8 - Virtual Basketball League"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "10"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "10 - Virtual Dog Racing"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "11"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "11 - Virtual Horse Classics"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "12"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "12 - Virtual Tennis In-Play"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "15"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "15 - Virtual Baseball In-Play"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "20"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "20 - Betgenius Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "21"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "21 - Betgenius Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "30"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "30 - VirtualSports"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "150"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "150 - FA LSport Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "151"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "151 - FA LSport Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "501"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "501 - Premium Cricket Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "503"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "503 - Premium Cricket Prematch"
                  }
                ]
              }
            ]
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 4,
            "x": 0,
            "y": 16
          },
          "hiddenSeries": false,
          "id": 10,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "time_series",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  date AS \"time\",\n  time_before_trading,\n  producer_id\nFROM sport_offering_${feed_type}timing\nWHERE\n  $__timeFilter(date)\nORDER BY producer_id, 1",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "delay"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_${feed_type}timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Time before trading",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:185",
              "format": "ms",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:186",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "ms"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "1 - Live Odds"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "3"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "3 - Betradar Ctrl"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "6"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "6 - Virtual football"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "8"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "8 - Virtual Basketball League"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "10"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "10 - Virtual Dog Racing"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "11"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "11 - Virtual Horse Classics"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "12"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "12 - Virtual Tennis In-Play"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "15"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "15 - Virtual Baseball In-Play"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "20"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "20 - Betgenius Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "21"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "21 - Betgenius Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "30"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "30 - VirtualSports"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "101"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "101 - Aggregator Live Odds"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "103"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "103 - Aggregator Betradar Ctrl"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "120"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "120 - Aggregator Betgenius Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "121"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "121 - Aggregator Betgenius Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "40"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "40 - Manual"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "150"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "150 - FA LSport Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "151"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "151 - FA LSport Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "501"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "501 - Premium Cricket Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "503"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "503 - Premium Cricket Prematch"
                  }
                ]
              }
            ]
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 5,
            "x": 4,
            "y": 16
          },
          "hiddenSeries": false,
          "id": 6,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "time_series",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  date AS \"time\",\n  time_in_trading,\n  producer_id\nFROM sport_offering_${feed_type}timing\nWHERE\n  $__timeFilter(date)\nORDER BY producer_id, 1",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "delay"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_${feed_type}timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "feed time delay in hubs/feeds",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:185",
              "format": "ms",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:186",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "ms"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "1 - Live Odds"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "3"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "3 - Betradar Ctrl"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "6"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "6 - Virtual football"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "8"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "8 - Virtual Basketball League"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "10"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "10 - Virtual Dog Racing"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "11"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "11 - Virtual Horse Classics"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "12"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "12 - Virtual Tennis In-Play"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "15"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "15 - Virtual Baseball In-Play"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "20"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "20 - Betgenius Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "21"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "21 - Betgenius Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "30"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "30 - VirtualSports"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "101"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "101 - Aggregator Live Odds"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "103"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "103 - Aggregator Betradar Ctrl"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "120"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "120 - Aggregator Betgenius Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "121"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "121 - Aggregator Betgenius Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "40"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "40 - Manual"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "150"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "150 - FA LSport Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "151"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "151 - FA LSport Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "501"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "501 - Premium Cricket Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "503"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "503 - Premium Cricket Prematch"
                  }
                ]
              }
            ]
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 5,
            "x": 9,
            "y": 16
          },
          "hiddenSeries": false,
          "id": 4,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "time_series",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  date AS \"time\",\n  redis_wait,\n  producer_id\nFROM sport_offering_${feed_type}timing\nWHERE\n  $__timeFilter(date)\nORDER BY producer_id, 1",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "delay"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_${feed_type}timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Feed delay redis wait",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:245",
              "format": "ms",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:246",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "ms"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "1 - Live Odds"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "3"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "3 - Betradar Ctrl"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "6"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "6 - Virtual football"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "8"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "8 - Virtual Basketball League"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "10"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "10 - Virtual Dog Racing"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "11"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "11 - Virtual Horse Classics"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "12"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "12 - Virtual Tennis In-Play"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "15"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "15 - Virtual Baseball In-Play"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "20"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "20 - Betgenius Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "21"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "21 - Betgenius Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "30"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "30 - VirtualSports"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "101"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "101 - Aggregator Live Odds"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "103"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "103 - Aggregator Betradar Ctrl"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "120"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "120 - Aggregator Betgenius Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "121"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "121 - Aggregator Betgenius Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "40"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "40 - Manual"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "150"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "150 - FA LSport Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "151"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "151 - FA LSport Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "501"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "501 - Premium Cricket Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "503"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "503 - Premium Cricket Prematch"
                  }
                ]
              }
            ]
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 5,
            "x": 14,
            "y": 16
          },
          "hiddenSeries": false,
          "id": 5,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "time_series",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  date AS \"time\",\n  time_in_offering,\n  producer_id\nFROM sport_offering_${feed_type}timing\nWHERE\n  $__timeFilter(date)\nORDER BY producer_id, 1",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "delay"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_${feed_type}timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Feed delay time in offering",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:305",
              "format": "ms",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:306",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "ms"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "1 - Live Odds"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "3"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "3 - Betradar Ctrl"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "6"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "6 - Virtual football"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "8"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "8 - Virtual Basketball League"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "10"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "10 - Virtual Dog Racing"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "11"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "11 - Virtual Horse Classics"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "12"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "12 - Virtual Tennis In-Play"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "15"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "15 - Virtual Baseball In-Play"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "20"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "20 - Betgenius Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "21"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "21 - Betgenius Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "30"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "30 - VirtualSports"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "101"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "101 - Aggregator Live Odds"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "103"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "103 - Aggregator Betradar Ctrl"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "120"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "120 - Aggregator Betgenius Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "121"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "121 - Aggregator Betgenius Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "40"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "40 - Manual"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "150"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "150 - FA LSport Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "151"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "151 - FA LSport Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "501"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "501 - Premium Cricket Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "503"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "503 - Premium Cricket Prematch"
                  }
                ]
              }
            ]
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 5,
            "x": 19,
            "y": 16
          },
          "hiddenSeries": false,
          "id": 15,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "time_series",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  date AS \"time\",\n  time_in_websocket,\n  producer_id\nFROM sport_offering_${feed_type}timing\nWHERE\n  $__timeFilter(date)\nORDER BY producer_id, 1",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "delay"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_${feed_type}timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Feed delay time in websocket",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:185",
              "format": "ms",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:186",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "ms"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "20"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "20 - Betgenius Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "21"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "21 - Betgenius Live"
                  }
                ]
              }
            ]
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 25
          },
          "hiddenSeries": false,
          "id": 12,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "time_series",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  date AS \"time\",\n  delay,\n  producer_id\nFROM sport_offering_${feed_type}timing\nWHERE\n  $__timeFilter(date)\n  AND\n  producer_id IN ('21', '20')\nORDER BY 1",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "delay"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_${feed_type}timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Feed delay for Betgenius",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "ms",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "ms"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "1 - Live Odds"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "3"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "3 - Betradar Ctrl"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "6"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "6 - Virtual football"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "8"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "8 - Virtual Basketball League"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "10"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "10 - Virtual Dog Racing"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "11"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "11 - Virtual Horse Classics"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "12"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "12 - Virtual Tennis In-Play"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "15"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "15 - Virtual Baseball In-Play"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "30"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "30 - VirtualSports"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "101"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "101 - Aggregator Live Odds"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "103"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "103 - Aggregator Betradar Ctrl"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "120"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "120 - Aggregator Betgenius Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "121"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "121 - Aggregator Betgenius Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "40"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "40 - Manual"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "150"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "150 - FA LSport Prematch"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byRegexp",
                  "options": "151"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "151 - FA LSport Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "501"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "501 - Premium Cricket Live"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "503"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "503 - Premium Cricket Prematch"
                  }
                ]
              }
            ]
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 25
          },
          "hiddenSeries": false,
          "id": 14,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "time_series",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  date AS \"time\",\n  delay,\n  producer_id\nFROM sport_offering_${feed_type}timing\nWHERE\n  $__timeFilter(date)\n  AND\n  producer_id != '21'\n  AND\n  producer_id != '20'\nORDER BY 1",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "delay"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_${feed_type}timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Feed delay by producer id without Betgenius",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "ms",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        }
      ],
      "refresh": "10s",
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": [
          {
            "allValue": null,
            "current": {
              "selected": false,
              "text": "Premium",
              "value": ""
            },
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": false,
            "label": "Feed Type",
            "multi": false,
            "name": "feed_type",
            "options": [
              {
                "selected": true,
                "text": "Premium",
                "value": ""
              },
              {
                "selected": false,
                "text": "Standard",
                "value": "standard_"
              }
            ],
            "query": "Premium :   ,Standard : standard_",
            "skipUrlSync": false,
            "type": "custom"
          }
        ]
      },
      "time": {
        "from": "now-5m",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "Sport-Offering-Delay",
      "uid": "qEKXxwqGk11fwfewfwfewfwfew",
      "version": 8
    }

apiVersion: v1
kind: ConfigMap
metadata:
  name: elasticsearch-index-pattern-sizes-dashboard
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: elasticsearch-cluster
data:
  elasticsearch-index-pattern-sizes.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "description": "Grafana Dashboards for Elasticsearch using Prometheus Datasource. Dashboard for Index Stats.",
      "editable": true,
      "gnetId": 13072,
      "graphTooltip": 1,
      "id": 926,
      "iteration": 1732111994912,
      "links": [
        {
          "asDropdown": true,
          "icon": "external link",
          "tags": [
            "elasticsearch"
          ],
          "title": "ES Dashboards",
          "type": "dashboards"
        }
      ],
      "panels": [
        {
          "aliasColors": {},
          "breakPoint": "50%",
          "cacheTimeout": null,
          "combine": {
            "label": "Others",
            "threshold": 0
          },
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fontSize": "80%",
          "format": "bytes",
          "gridPos": {
            "h": 11,
            "w": 23,
            "x": 0,
            "y": 0
          },
          "id": 257,
          "interval": null,
          "legend": {
            "show": true,
            "sort": "current",
            "sortDesc": true,
            "values": true
          },
          "legendType": "Under graph",
          "links": [],
          "nullPointMode": "connected",
          "pieType": "pie",
          "pluginVersion": "7.4.2",
          "strokeWidth": 1,
          "targets": [
            {
              "expr": "sum by(pattern) (label_replace(\n  elasticsearch_indices_store_size_bytes_total,\n  \"pattern\",\n  \"$1\",\n  \"index\",\n  \"(.+)-\\\\d{4}-\\\\d{2}-\\\\d{2}\"\n))",
              "interval": "",
              "legendFormat": "{{pattern}}",
              "refId": "A"
            }
          ],
          "title": "All index patterns by size via PieChart",
          "type": "grafana-piechart-panel",
          "valueName": "current"
        },
        {
          "aliasColors": {},
          "bars": false,
          "cacheTimeout": null,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": null,
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "thresholds": {
                "mode": "absolute",
                "steps": []
              },
              "unit": "bytes"
            },
            "overrides": []
          },
          "fill": 0,
          "fillGradient": 5,
          "gridPos": {
            "h": 18,
            "w": 23,
            "x": 0,
            "y": 11
          },
          "hiddenSeries": false,
          "id": 255,
          "interval": null,
          "legend": {
            "alignAsTable": true,
            "avg": true,
            "current": true,
            "hideEmpty": false,
            "hideZero": true,
            "max": false,
            "min": false,
            "rightSide": false,
            "show": true,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 3,
          "links": [],
          "nullPointMode": "connected",
          "options": {
            "alertThreshold": false
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": true,
          "targets": [
            {
              "$$hashKey": "object:2939",
              "aggregation": "Last",
              "decimals": 2,
              "displayAliasType": "Warning / Critical",
              "displayType": "Regular",
              "displayValueWithAlias": "Never",
              "expr": "sum by(pattern) (label_replace(\n  elasticsearch_indices_store_size_bytes_total,\n  \"pattern\",\n  \"$1\",\n  \"index\",\n  \"(.+)-\\\\d{4}-\\\\d{2}-\\\\d{2}\"\n))",
              "interval": "",
              "legendFormat": "{{pattern}}",
              "refId": "A",
              "units": "none",
              "valueHandler": "Number Threshold"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "All index patterns by size",
          "tooltip": {
            "shared": true,
            "sort": 2,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:3162",
              "format": "bytes",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:3163",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        }
      ],
      "refresh": "10s",
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": [
          {
            "allValue": null,
            "current": {
              "selected": false,
              "text": "prod-logs-2",
              "value": "prod-logs-2"
            },
            "datasource": "Prometheus",
            "definition": "label_values(elasticsearch_indices_store_size_bytes_total, cluster)",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": false,
            "label": "Cluster",
            "multi": false,
            "name": "cluster",
            "options": [],
            "query": {
              "query": "label_values(elasticsearch_indices_store_size_bytes_total, cluster)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 1,
            "tagValuesQuery": null,
            "tags": [],
            "tagsQuery": null,
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {
              "selected": true,
              "text": [
                "All"
              ],
              "value": [
                "$__all"
              ]
            },
            "datasource": "Prometheus",
            "definition": "label_values(elasticsearch_indices_docs{instance=\"$instance\",cluster=\"$cluster\", name!=\"\"},name)",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": true,
            "label": "Node name",
            "multi": true,
            "name": "name",
            "options": [],
            "query": {
              "query": "label_values(elasticsearch_indices_docs{instance=\"$instance\",cluster=\"$cluster\", name!=\"\"},name)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 1,
            "tagValuesQuery": null,
            "tags": [],
            "tagsQuery": null,
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {
              "selected": false,
              "text": "************:9114",
              "value": "************:9114"
            },
            "datasource": "Prometheus",
            "definition": "label_values(elasticsearch_indices_store_size_bytes_total{cluster=\"$cluster\"}, instance)",
            "description": null,
            "error": null,
            "hide": 2,
            "includeAll": false,
            "label": "Source of metrics",
            "multi": false,
            "name": "instance",
            "options": [],
            "query": {
              "query": "label_values(elasticsearch_indices_store_size_bytes_total{cluster=\"$cluster\"}, instance)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 1,
            "tagValuesQuery": null,
            "tags": [],
            "tagsQuery": null,
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {
              "selected": false,
              "text": "uf-sdk-traffic-log-2024-11-17",
              "value": "uf-sdk-traffic-log-2024-11-17"
            },
            "datasource": "Prometheus",
            "definition": "label_values(elasticsearch_indices_store_size_bytes_total{cluster=\"$cluster\"}, index)",
            "description": null,
            "error": null,
            "hide": 2,
            "includeAll": false,
            "label": "Index",
            "multi": false,
            "name": "index",
            "options": [],
            "query": {
              "query": "label_values(elasticsearch_indices_store_size_bytes_total{cluster=\"$cluster\"}, index)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 2,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "auto": true,
            "auto_count": 30,
            "auto_min": "10s",
            "current": {
              "selected": false,
              "text": "5m",
              "value": "5m"
            },
            "description": null,
            "error": null,
            "hide": 2,
            "label": "Rate Interval",
            "name": "interval",
            "options": [
              {
                "selected": false,
                "text": "auto",
                "value": "$__auto_interval_interval"
              },
              {
                "selected": true,
                "text": "5m",
                "value": "5m"
              },
              {
                "selected": false,
                "text": "10m",
                "value": "10m"
              },
              {
                "selected": false,
                "text": "30m",
                "value": "30m"
              },
              {
                "selected": false,
                "text": "1h",
                "value": "1h"
              },
              {
                "selected": false,
                "text": "6h",
                "value": "6h"
              },
              {
                "selected": false,
                "text": "12h",
                "value": "12h"
              },
              {
                "selected": false,
                "text": "1d",
                "value": "1d"
              },
              {
                "selected": false,
                "text": "7d",
                "value": "7d"
              },
              {
                "selected": false,
                "text": "14d",
                "value": "14d"
              },
              {
                "selected": false,
                "text": "30d",
                "value": "30d"
              }
            ],
            "query": "5m,10m,30m,1h,6h,12h,1d,7d,14d,30d",
            "refresh": 2,
            "skipUrlSync": false,
            "type": "interval"
          }
        ]
      },
      "time": {
        "from": "now-6h",
        "to": "now"
      },
      "timepicker": {
        "refresh_intervals": [
          "1s",
          "5s",
          "10s",
          "30s",
          "1m",
          "5m",
          "15m",
          "30m",
          "1h",
          "2h",
          "1d"
        ],
        "time_options": [
          "5m",
          "15m",
          "1h",
          "6h",
          "12h",
          "24h",
          "2d",
          "7d",
          "30d"
        ]
      },
      "timezone": "",
      "title": "Elasticsearch - Index Pattern Sizes",
      "uid": "4BMaAI7Hz",
      "version": 6
    }
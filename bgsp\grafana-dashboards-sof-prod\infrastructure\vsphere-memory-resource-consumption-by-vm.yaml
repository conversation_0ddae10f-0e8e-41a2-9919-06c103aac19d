apiVersion: v1
kind: ConfigMap
metadata:
  name: vsphere-memory-resource-consumption-by-vm
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: infrastructure
data:
  vsphere-memory-resource-consumption-by-vm.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": 955,
      "iteration": 1738163594658,
      "links": [],
      "panels": [
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 0
          },
          "hiddenSeries": false,
          "id": 27,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeat": "hostnode",
          "repeatDirection": "h",
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 0
          },
          "hiddenSeries": false,
          "id": 28,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 9
          },
          "hiddenSeries": false,
          "id": 29,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 9
          },
          "hiddenSeries": false,
          "id": 30,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 18
          },
          "hiddenSeries": false,
          "id": 31,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 18
          },
          "hiddenSeries": false,
          "id": 32,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 27
          },
          "hiddenSeries": false,
          "id": 33,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 27
          },
          "hiddenSeries": false,
          "id": 34,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 36
          },
          "hiddenSeries": false,
          "id": 35,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 36
          },
          "hiddenSeries": false,
          "id": 36,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 45
          },
          "hiddenSeries": false,
          "id": 37,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 45
          },
          "hiddenSeries": false,
          "id": 38,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 54
          },
          "hiddenSeries": false,
          "id": 39,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 54
          },
          "hiddenSeries": false,
          "id": 40,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 63
          },
          "hiddenSeries": false,
          "id": 41,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 63
          },
          "hiddenSeries": false,
          "id": 42,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 72
          },
          "hiddenSeries": false,
          "id": 43,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 72
          },
          "hiddenSeries": false,
          "id": 44,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 81
          },
          "hiddenSeries": false,
          "id": 45,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 81
          },
          "hiddenSeries": false,
          "id": 46,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 90
          },
          "hiddenSeries": false,
          "id": 47,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 90
          },
          "hiddenSeries": false,
          "id": 48,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 99
          },
          "hiddenSeries": false,
          "id": 49,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 99
          },
          "hiddenSeries": false,
          "id": 50,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 108
          },
          "hiddenSeries": false,
          "id": 51,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 108
          },
          "hiddenSeries": false,
          "id": 52,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "decimals": 2,
          "description": "The following panel provides information for memory usage on given hostnode with summarize for the memory usage for the hostnode itself",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "percent"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 117
          },
          "hiddenSeries": false,
          "id": 53,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "maxPerRow": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "repeatDirection": "h",
          "repeatIteration": 1738163594658,
          "repeatPanelId": 27,
          "scopedVars": {
            "hostnode": {
              "selected": false,
              "text": "*************",
              "value": "*************"
            }
          },
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\", vmname=~\"$vms\"}",
              "interval": "",
              "legendFormat": "{{vmname}}",
              "refId": "A"
            },
            {
              "expr": "vsphere_host_mem_usage_average{clustername=\"$cluster\",esxhostname=~\"$hostnode\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "HOST",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Memory usage - $hostnode",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:55",
              "format": "percent",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": "0",
              "show": true
            },
            {
              "$$hashKey": "object:56",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        }
      ],
      "refresh": "10s",
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": [
          {
            "allValue": ".*",
            "current": {
              "selected": true,
              "text": "sof1-prod",
              "value": "sof1-prod"
            },
            "datasource": null,
            "definition": "label_values(vsphere_host_mem_usage_average, clustername)",
            "description": "Cluster name from query",
            "error": null,
            "hide": 0,
            "includeAll": false,
            "label": null,
            "multi": false,
            "name": "cluster",
            "options": [],
            "query": {
              "query": "label_values(vsphere_host_mem_usage_average, clustername)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 2,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": ".*",
            "current": {
              "selected": true,
              "text": [
                "All"
              ],
              "value": [
                "$__all"
              ]
            },
            "datasource": null,
            "definition": "label_values(vsphere_host_mem_usage_average{clustername=\"$cluster\"}, esxhostname)",
            "description": "Host nodes in given cluster",
            "error": null,
            "hide": 0,
            "includeAll": true,
            "label": "Host node",
            "multi": true,
            "name": "hostnode",
            "options": [],
            "query": {
              "query": "label_values(vsphere_host_mem_usage_average{clustername=\"$cluster\"}, esxhostname)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 3,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": ".*",
            "current": {
              "selected": true,
              "text": [
                "All"
              ],
              "value": [
                "$__all"
              ]
            },
            "datasource": null,
            "definition": "label_values(vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\"}, vmname)",
            "description": "Virtual Machines in given cluster on given hostnode",
            "error": null,
            "hide": 0,
            "includeAll": true,
            "label": "Virtual Machines",
            "multi": true,
            "name": "vms",
            "options": [],
            "query": {
              "query": "label_values(vsphere_vm_mem_usage_average{clustername=\"$cluster\", esxhostname=~\"$hostnode\"}, vmname)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 1,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          }
        ]
      },
      "time": {
        "from": "now-7d",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "vSphere memory consumption by VM",
      "uid": "9E6rv5E7zbyVMmem",
      "version": 11
    }

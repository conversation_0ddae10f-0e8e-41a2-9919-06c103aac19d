apiVersion: v1
kind: ConfigMap
metadata:
  name: kibana-alerts-only-dashboard
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: NOC-dashboards
data:
  kibana-alerts-only-dashboard.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": 1001,
      "links": [],
      "panels": [
        {
          "alert": {
            "alertRuleTags": {
              "Service": "CRM",
              "Severity": "Critical"
            },
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    250000
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "1h",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "sum"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "1m",
            "frequency": "1m",
            "handler": 1,
            "message": "https://kibana-pl2.egt-digital.com/app/discover#/view/476a8d70-5ab1-11ed-8b6a-fbdb491d4e24?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1h,to:now))&_a=(columns:!(message),filters:!(),grid:(),hideChart:!f,index:'20e29950-506e-11ee-a7b2-256680d29499',interval:auto,query:(language:kuery,query:'NOT%20%22S:AUDIT%22%20AND%20NOT%20%22ews_dotnet_common.Extensions.AuditLogMiddleware%22%20AND%20NOT%20%22SegmentStoSApi%22%20AND%20NOT%20%22PlayerSToSSegmentController%22%20AND%20NOT%20%22SegmentsByFormulaService%22%20AND%20NOT%20%22ews_crm.Controllers.SToS.PlayerSToSSegmentController%22%20AND%20NOT%20%22EWS.CRM.Application.Players.PlayerSegmentsByFormulaService%22%20AND%20NOT%20%22Polling%20for%22%20AND%20NOT%20%22ews_crm.Controllers.SToS.PlayerSToSController%22%20AND%20NOT%20%22EWS.CRM.Application.PlayerOperations.PlayerSegmentsByFormulaService%22%20AND%20NOT%20%22ews_dotnet_common.Filters.GlobalExceptionFilter%22%20AND%20NOT%20%22%5BS2S%5D%22'),sort:!(!('@timestamp',desc)))",
            "name": "CRM is above 250K",
            "noDataState": "alerting",
            "notifications": [
              {
                "uid": "pagerduty1"
              },
              {
                "uid": "I9C9By5Vk"
              }
            ]
          },
          "aliasColors": {},
          "bars": true,
          "cacheTimeout": null,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Elasticsearch-ews30-crm",
          "decimals": null,
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "thresholds": {
                "mode": "absolute",
                "steps": []
              },
              "unit": "none"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 0,
            "y": 0
          },
          "hiddenSeries": false,
          "id": 2,
          "interval": null,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 400,
            "total": true,
            "values": true
          },
          "lines": false,
          "linewidth": 1,
          "links": [],
          "nullPointMode": "null",
          "options": {
            "alertThreshold": false
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "$$hashKey": "object:3933",
              "aggregation": "Last",
              "alias": "ews30-crm-*",
              "bucketAggs": [
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "auto",
                    "min_doc_count": "0",
                    "trimEdges": "0"
                  },
                  "type": "date_histogram"
                }
              ],
              "decimals": 2,
              "displayAliasType": "Warning / Critical",
              "displayType": "Regular",
              "displayValueWithAlias": "Never",
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "NOT \"S:AUDIT\" AND NOT \"ews_dotnet_common.Extensions.AuditLogMiddleware\" AND NOT \"SegmentStoSApi\" AND NOT \"PlayerSToSSegmentController\" AND NOT \"SegmentsByFormulaService\" AND NOT \"ews_crm.Controllers.SToS.PlayerSToSSegmentController\" AND NOT \"EWS.CRM.Application.Players.PlayerSegmentsByFormulaService\" AND NOT \"Polling for\" AND NOT \"ews_crm.Controllers.SToS.PlayerSToSController\" AND NOT \"EWS.CRM.Application.PlayerOperations.PlayerSegmentsByFormulaService\" AND NOT \"ews_dotnet_common.Filters.GlobalExceptionFilter\" AND NOT \"[S2S]\"",
              "refId": "A",
              "timeField": "@timestamp",
              "units": "none",
              "valueHandler": "Number Threshold"
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": false,
              "line": false,
              "op": "gt",
              "value": 150000,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "CRM > 250K",
          "tooltip": {
            "shared": false,
            "sort": 0,
            "value_type": "individual"
          },
          "transparent": true,
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "series",
            "name": null,
            "show": true,
            "values": [
              "total"
            ]
          },
          "yaxes": [
            {
              "$$hashKey": "object:779",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:780",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": true,
            "alignLevel": null
          }
        },
        {
          "alert": {
            "alertRuleTags": {
              "Severity": "Critical"
            },
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    110000
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "1h",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "sum"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "2m",
            "frequency": "1m",
            "handler": 1,
            "message": "https://kibana-pl2.egt-digital.com/app/discover#/view/76fd81f0-5ab1-11ed-8b6a-fbdb491d4e24?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1h,to:now))&_a=(columns:!(message),filters:!(('$state':(store:appState),meta:(alias:!n,disabled:!f,index:'1bfeb220-4a02-11ed-8b6a-fbdb491d4e24',key:kubernetes.container_name,negate:!t,params:(query:ews-oauth-ingester),type:phrase),query:(match_phrase:(kubernetes.container_name:ews-oauth-ingester)))),grid:(),hideChart:!f,index:'1bfeb220-4a02-11ed-8b6a-fbdb491d4e24',interval:auto,query:(language:kuery,query:'NOT%20%22Total%20messages%20consumed%22%20AND%20NOT%20%22is%20associated%20with%20the%20provided%20access%20token%22%20AND%20NOT%20%22Access%20token%20already%20revoked%20or%20could%20not%20be%20revoked%22%20AND%20NOT%20%22Call%20CAPI%22%20AND%20NOT%20%22s2s%22'),sort:!(!('@timestamp',desc)))",
            "name": "OAUTH > 110k alert",
            "noDataState": "alerting",
            "notifications": [
              {
                "uid": "pagerduty1"
              }
            ]
          },
          "aliasColors": {},
          "bars": true,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Elasticsearch-ews-oauth",
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "thresholds": {
                "mode": "absolute",
                "steps": []
              }
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 9,
            "w": 12,
            "x": 12,
            "y": 0
          },
          "hiddenSeries": false,
          "id": 4,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": true,
            "values": true
          },
          "lines": false,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "alias": "ews-oauth*",
              "bucketAggs": [
                {
                  "id": "3",
                  "settings": {
                    "filters": [
                      {
                        "label": "",
                        "query": "NOT kubernetes.container_name: \"ews-oauth-ingester\""
                      }
                    ]
                  },
                  "type": "filters"
                },
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "NOT \"Total messages consumed\" AND NOT \"is associated with the provided access token\" AND NOT \"Access token already revoked or could not be revoked\" AND NOT \"Call CAPI\" AND NOT \"s2s\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "gt",
              "value": 110000,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "OAUTH > 110k",
          "tooltip": {
            "shared": false,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "series",
            "name": null,
            "show": true,
            "values": [
              "total"
            ]
          },
          "yaxes": [
            {
              "$$hashKey": "object:69",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:70",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "alert": {
            "alertRuleTags": {},
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    350
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "1h",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "sum"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "2m",
            "frequency": "10sec",
            "handler": 1,
            "message": "https://kibana-pl2.egt-digital.com/app/discover#/view/f68de570-a612-11ed-a7b2-256680d29499?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1h,to:now))&_a=(columns:!(),filters:!(),grid:(),hideChart:!f,index:'724c68f0-ad94-11ef-8c3e-db096a40ec21',interval:auto,query:(language:kuery,query:'(%22Error%22%20or%20%22L:Error%22%20or%20%22Exception%22%20or%20%22Object%20ref%22)'),sort:!(!('@timestamp',desc)))",
            "name": "NRA Time out alert",
            "noDataState": "alerting",
            "notifications": [
              {
                "uid": "pagerduty1"
              }
            ]
          },
          "aliasColors": {},
          "bars": true,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Elasticsearch-ews-30-regul-comp",
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "thresholds": {
                "mode": "absolute",
                "steps": []
              }
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 6,
            "y": 9
          },
          "hiddenSeries": false,
          "id": 6,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": false,
            "hideEmpty": false,
            "hideZero": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": true,
            "values": true
          },
          "lines": false,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "(\"Error\" or \"L:Error\" or \"Exception\" or \"Object ref\")",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "gt",
              "value": 350,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "NRA vulnerable players search",
          "tooltip": {
            "shared": false,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "series",
            "name": null,
            "show": true,
            "values": [
              "total"
            ]
          },
          "yaxes": [
            {
              "$$hashKey": "object:514",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:515",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "alert": {
            "alertRuleTags": {
              "Severity": "Critical"
            },
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    50
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "1h",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "sum"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "1m",
            "frequency": "30sec",
            "handler": 1,
            "message": "https://kibana-pl2.egt-digital.com/app/discover#/view/30f70f10-c10c-11ee-bba2-77a9fc5fd2da?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1h,to:now))&_a=(columns:!(),filters:!(),grid:(),hideChart:!f,index:e169bb20-4acc-11ed-8b6a-fbdb491d4e24,interval:auto,query:(language:kuery,query:'(%22L:Error%22%20or%20%22Error%22%20or%20%22error%22%20or%20error%20or%20%22unhandled%20exception%22%20or%20%22FAIL%22)%20and%20not%20%22L:Warning%22%20and%20not%20%22L:Information%22%20and%20not%20%22PlayerActivityChoice,%20error%20fetching%20player%20bonuses%22%20and%20not%20%22PlayerActivityChoice%22%20and%20not%20%22EWS.Bonuses.Services.Bonuses.PlayerBonusesExpirationService%22%20and%20not%20%22%2Fapi%2Fsportsapi%2Fpublic%2Fsport-events%2Frelated-sport-events%22%20and%20not%20%22GetRelatedSportEventIds%20%22'),sort:!(!('@timestamp',desc)))",
            "name": "Campaign Errors alert",
            "noDataState": "alerting",
            "notifications": [
              {
                "uid": "pagerduty1"
              }
            ]
          },
          "aliasColors": {},
          "bars": true,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Elasticsearch-ews-bonuses",
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "displayName": "ERRORS",
              "thresholds": {
                "mode": "absolute",
                "steps": []
              }
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 17
          },
          "hiddenSeries": false,
          "id": 24,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": true,
            "values": true
          },
          "lines": false,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": false
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "alias": "ews-bonuses",
              "bucketAggs": [
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "(\"L:Error\" OR \"Error\" OR \"error\" OR \"unhandled exception\" OR \"FAIL\") AND NOT \"L:Warning\" AND NOT \"L:Information\" AND NOT \"PlayerActivityChoice, error fetching player bonuses\" AND NOT \"PlayerActivityChoice\" AND NOT \"EWS.Bonuses.Services.Bonuses.PlayerBonusesExpirationService\" AND NOT \"/api/sportsapi/public/sport-events/related-sport-events\" AND NOT \"GetRelatedSportEventIds \"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": false,
              "line": false,
              "op": "gt",
              "value": 50,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "CAMPAIGN ERRORS",
          "tooltip": {
            "shared": false,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "series",
            "name": null,
            "show": true,
            "values": [
              "total"
            ]
          },
          "yaxes": [
            {
              "$$hashKey": "object:1071",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:1072",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "alert": {
            "alertRuleTags": {
              "Severity": "Critical"
            },
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    50
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "1h",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "avg"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "1m",
            "frequency": "30sec",
            "handler": 1,
            "message": "https://kibana-pl2.egt-digital.com/app/discover#/view/5ebfc5e0-b085-11ef-8c3e-db096a40ec21?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1h,to:now))&_a=(columns:!(),filters:!(),grid:(),hideChart:!f,index:ffbde610-ad57-11ef-8c3e-db096a40ec21,interval:auto,query:(language:kuery,query:'(%22L:Error%22%20or%20%22Error%22%20or%20%22error%22%20or%20error%20or%20%22unhandled%20exception%22)%20and%20not%20%22L:Warning%22%20and%20not%20%22L:Information%22'),sort:!(!('@timestamp',desc)))",
            "name": "PROMO GAMES alert",
            "noDataState": "alerting",
            "notifications": [
              {
                "uid": "pagerduty1"
              }
            ]
          },
          "aliasColors": {},
          "bars": true,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Elasticsearch-ews30-platform-games",
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "displayName": "ERRORS",
              "thresholds": {
                "mode": "absolute",
                "steps": []
              }
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 17
          },
          "hiddenSeries": false,
          "id": 26,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": true,
            "values": true
          },
          "lines": false,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": false
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "alias": "ews30-platform-games",
              "bucketAggs": [
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "(\"L:Error\" OR \"Error\" OR \"error\" OR error OR \"unhandled exception\") AND NOT \"L:Warning\" AND NOT \"L:Information\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": false,
              "line": false,
              "op": "gt",
              "value": 50,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "PROMO GAMES",
          "tooltip": {
            "shared": false,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "series",
            "name": null,
            "show": true,
            "values": [
              "total"
            ]
          },
          "yaxes": [
            {
              "$$hashKey": "object:1270",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:1271",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "alert": {
            "alertRuleTags": {
              "Severity": "Critical"
            },
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    1400
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "1h",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "sum"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "2m",
            "frequency": "30sec",
            "handler": 1,
            "message": "https://kibana-pl2.egt-digital.com/app/discover#/view/6999bf30-4bb9-11ed-8b6a-fbdb491d4e24?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1h,to:now))&_a=(columns:!(severity,message),filters:!(('$state':(store:appState),meta:(alias:!n,disabled:!f,index:'847c5ce0-49e3-11ed-8b6a-fbdb491d4e24',key:severity,negate:!f,params:(query:HIGH),type:phrase),query:(match_phrase:(severity:HIGH))),('$state':(store:appState),meta:(alias:!n,disabled:!f,index:'847c5ce0-49e3-11ed-8b6a-fbdb491d4e24',key:message,negate:!f,params:(query:Error),type:phrase),query:(match_phrase:(message:Error)))),grid:(),hideChart:!f,index:'5bdd6610-09de-11ee-a7b2-256680d29499',interval:auto,query:(language:kuery,query:ERROR),sort:!(!('@timestamp',asc)))",
            "name": "Payments Errors alert",
            "noDataState": "alerting",
            "notifications": [
              {
                "uid": "pagerduty1"
              }
            ]
          },
          "aliasColors": {},
          "bars": true,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Elasticsearch-ews90-payment",
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "displayName": "ERRORS",
              "thresholds": {
                "mode": "absolute",
                "steps": []
              }
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 6,
            "y": 25
          },
          "hiddenSeries": false,
          "id": 16,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": true,
            "values": true
          },
          "lines": false,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "\"Error\" AND \"HIGH\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "gt",
              "value": 1400,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "PAYMENTS",
          "tooltip": {
            "shared": false,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "series",
            "name": null,
            "show": true,
            "values": [
              "total"
            ]
          },
          "yaxes": [
            {
              "$$hashKey": "object:78",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:79",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "alert": {
            "alertRuleTags": {
              "Severity": "Critical"
            },
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    45
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "1h",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "sum"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "1m",
            "frequency": "30sec",
            "handler": 1,
            "message": "https://kibana-pl2.egt-digital.com/app/discover#/view/1e55aa50-21b2-11ee-a7b2-256680d29499?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1h,to:now))&_a=(columns:!(message),filters:!(),grid:(),hideChart:!f,index:'5bdd6610-09de-11ee-a7b2-256680d29499',interval:auto,query:(language:kuery,query:'%22APG_GENERATE_TOKEN_ERROR%22%20OR%20%22CREATE_CREDIT_CARD_ERROR%22%20OR%20%22APG_AUTHORIZE_ERROR%22%20OR%20%22APG_TOKEN_GENERATION_ERROR%22%20OR%20%22APG_TOKEN_REQUEST_FAILIURE%22%20OR%20%22APG_AUTHORIZE_ERROR%22%20OR%20%22EASYPAY_API_CALL_ERROR%22%20OR%20%22EASYPAY_DEPOSIT_ERROR%22%20OR%20%22EASYPAY_WITHDRAW_ERROR%22%20OR%20%22EPAY_DEPOSIT_ERROR%22%20OR%20%22EASYPAY_WITHDRAW_ERROR%22%20OR%20%22FASTPAY_API_CALL_ERROR%22'),sort:!(!('@timestamp',desc)))",
            "name": "Payment Gateway  Errors alert",
            "noDataState": "alerting",
            "notifications": [
              {
                "uid": "pagerduty1"
              }
            ]
          },
          "aliasColors": {},
          "bars": true,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Elasticsearch-ews90-payment",
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "displayName": "CRITICAL",
              "thresholds": {
                "mode": "absolute",
                "steps": []
              }
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 33
          },
          "hiddenSeries": false,
          "id": 10,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": true,
            "values": true
          },
          "lines": false,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "alias": "ews90-payment-*",
              "bucketAggs": [
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "\"APG_GENERATE_TOKEN_ERROR\" OR \"CREATE_CREDIT_CARD_ERROR\" OR \"APG_AUTHORIZE_ERROR\" OR \"APG_TOKEN_GENERATION_ERROR\" OR \"APG_TOKEN_REQUEST_FAILIURE\" OR \"APG_AUTHORIZE_ERROR\" OR \"EASYPAY_API_CALL_ERROR\" OR \"EASYPAY_DEPOSIT_ERROR\" OR \"EASYPAY_WITHDRAW_ERROR\" OR \"EPAY_DEPOSIT_ERROR\" OR \"EASYPAY_WITHDRAW_ERROR\" OR \"FASTPAY_API_CALL_ERROR\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "gt",
              "value": 45,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Payment Gateway  Errors",
          "tooltip": {
            "shared": false,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "series",
            "name": null,
            "show": true,
            "values": [
              "total"
            ]
          },
          "yaxes": [
            {
              "$$hashKey": "object:262",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:263",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "alert": {
            "alertRuleTags": {
              "Severity": "Warning"
            },
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    75
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "1h",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "sum"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "1m",
            "frequency": "30sec",
            "handler": 1,
            "message": "https://kibana-pl2.egt-digital.com/app/discover#/view/a22d09e0-21b2-11ee-a7b2-256680d29499?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1h,to:now))&_a=(columns:!(message),filters:!(),grid:(),hideChart:!f,index:'5bdd6610-09de-11ee-a7b2-256680d29499',interval:auto,query:(language:kuery,query:'%22CREDIT_CARD_NOT_FOUND%22%20OR%20%22APG_DEPOSIT_FAILED_TO_GET_ACQUIRER%22%20OR%20%22APG_CALLBACK_TRANSFER_STATUS_IS_NOT_CORRECT%22%20OR%20%22APG_CALLBACK_STATUS_ERROR%22%20OR%20%22JSON_PARSING_ERROR%22%20OR%20%22APG_WITHDRAWAL_REQUEST_FAILIURE%22%20OR%20%22APG_ACQUIRER_NOT_FOUND%22%20OR%20%22EASYPAY_CANCEL_UNKNOWN_RESPONSE%22%20OR%20%22EASYPAY_CANCEL_STATE_API_ERROR%22%20OR%20%22EASYPAY_CANCEL_STATE_RESPONSE_NOT_OK%22%20OR%20%22ERROR_CREATING_TRANSFER%22%20OR%20%22EASYPAY_CANCEL_UNKNOWN_RESPONSE%22%20OR%20%22EASYPAY_CANCEL_STATE_API_ERROR%22%20OR%20%22EASYPAY_CANCEL_STATE_RESPONSE_NOT_OK%22'),sort:!(!('@timestamp',desc)))",
            "name": "Payment Gateway Warnings alert",
            "noDataState": "alerting",
            "notifications": [
              {
                "uid": "pagerduty1"
              }
            ]
          },
          "aliasColors": {},
          "bars": true,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Elasticsearch-ews90-payment",
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "displayName": "WARNING",
              "thresholds": {
                "mode": "absolute",
                "steps": []
              }
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 11,
            "x": 12,
            "y": 33
          },
          "hiddenSeries": false,
          "id": 12,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": true,
            "values": true
          },
          "lines": false,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "alias": "ews90-payment-*",
              "bucketAggs": [
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "\"CREDIT_CARD_NOT_FOUND\" OR \"APG_DEPOSIT_FAILED_TO_GET_ACQUIRER\" OR \"APG_CALLBACK_TRANSFER_STATUS_IS_NOT_CORRECT\" OR \"APG_CALLBACK_STATUS_ERROR\" OR \"JSON_PARSING_ERROR\" OR \"APG_WITHDRAWAL_REQUEST_FAILIURE\" OR \"APG_ACQUIRER_NOT_FOUND\" OR \"EASYPAY_CANCEL_UNKNOWN_RESPONSE\" OR \"EASYPAY_CANCEL_STATE_API_ERROR\" OR \"EASYPAY_CANCEL_STATE_RESPONSE_NOT_OK\" OR \"ERROR_CREATING_TRANSFER\" OR \"EASYPAY_CANCEL_UNKNOWN_RESPONSE\" OR \"EASYPAY_CANCEL_STATE_API_ERROR\" OR \"EASYPAY_CANCEL_STATE_RESPONSE_NOT_OK\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "gt",
              "value": 75,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Payment Gateway Warnings",
          "tooltip": {
            "shared": false,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "series",
            "name": null,
            "show": true,
            "values": [
              "total"
            ]
          },
          "yaxes": [
            {
              "$$hashKey": "object:430",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:431",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "alert": {
            "alertRuleTags": {
              "Severity": "Critical"
            },
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    150
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "1h",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "sum"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "1m",
            "frequency": "1m",
            "handler": 1,
            "message": "https://kibana-pl2.egt-digital.com/app/discover#/view/32d5b520-4bba-11ed-8b6a-fbdb491d4e24?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1h,to:now))&_a=(columns:!(severity,message),filters:!(('$state':(store:appState),meta:(alias:!n,disabled:!f,index:'03ed6e60-49df-11ed-8b6a-fbdb491d4e24',key:message,negate:!t,params:(query:SMBG),type:phrase),query:(match_phrase:(message:SMBG)))),grid:(),hideChart:!f,index:a23a14f0-2e0e-11ef-8c3e-db096a40ec21,interval:auto,query:(language:kuery,query:'%22ST:MISSING_BET_COVERAGE%22%20AND%20%22T:EVOLUTION_BET_COVERAGE%22%20and%20not%20%22GoldbarRo0000001%22%20and%20not%20%22DoubleBallRou001%22'),sort:!(!('@timestamp',asc)))",
            "name": "MISSING_BET_COVERAGE alert",
            "noDataState": "alerting",
            "notifications": [
              {
                "uid": "pagerduty1"
              }
            ]
          },
          "aliasColors": {},
          "bars": true,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Elastic-ews90-gaming-wallet-evolution",
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "thresholds": {
                "mode": "absolute",
                "steps": []
              }
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 6,
            "y": 41
          },
          "hiddenSeries": false,
          "id": 8,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": true,
            "values": true
          },
          "lines": false,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "id": "3",
                  "settings": {
                    "filters": [
                      {
                        "label": "ERRORS",
                        "query": "NOT message: \"SMBG\""
                      }
                    ]
                  },
                  "type": "filters"
                },
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "\"ST:MISSING_BET_COVERAGE\" AND \"T:EVOLUTION_BET_COVERAGE\" and not \"GoldbarRo0000001\" and not \"DoubleBallRou001\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "gt",
              "value": 150,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "MISSING_BET_COVERAGE",
          "tooltip": {
            "shared": false,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "series",
            "name": null,
            "show": true,
            "values": [
              "total"
            ]
          },
          "yaxes": [
            {
              "$$hashKey": "object:578",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:579",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "alert": {
            "alertRuleTags": {
              "Severity": "Critical"
            },
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    1000
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "1h",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "sum"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "1m",
            "frequency": "30sec",
            "handler": 1,
            "message": "https://kibana-pl2.egt-digital.com/app/discover#/view/90cdb760-4bb8-11ed-8b6a-fbdb491d4e24?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1h,to:now))&_a=(columns:!(message,_type),filters:!(('$state':(store:appState),meta:(alias:!n,disabled:!f,index:'5bace180-4a02-11ed-8b6a-fbdb491d4e24',key:logLevel,negate:!f,params:(query:error),type:phrase),query:(match_phrase:(logLevel:error)))),grid:(),hideChart:!f,index:'5bace180-4a02-11ed-8b6a-fbdb491d4e24',interval:auto,query:(language:kuery,query:''),sort:!(!('@timestamp',desc)))",
            "name": "Sport Events Offering Errors alert",
            "noDataState": "alerting",
            "notifications": [
              {
                "uid": "pagerduty1"
              }
            ]
          },
          "aliasColors": {},
          "bars": true,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Elasticsearch-sport-events-offering",
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "displayName": "ERRORS",
              "thresholds": {
                "mode": "absolute",
                "steps": []
              }
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 49
          },
          "hiddenSeries": false,
          "id": 14,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": true,
            "values": true
          },
          "lines": false,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "alias": "ews-sport-event-offering",
              "bucketAggs": [
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "Error",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "gt",
              "value": 1000,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Sport Events Offering",
          "tooltip": {
            "shared": false,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "series",
            "name": null,
            "show": true,
            "values": [
              "total"
            ]
          },
          "yaxes": [
            {
              "$$hashKey": "object:789",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:790",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "alert": {
            "alertRuleTags": {
              "Severity": "Critical"
            },
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    1000
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "1h",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "sum"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "1m",
            "frequency": "30sec",
            "handler": 1,
            "message": "https://kibana-pl2.egt-digital.com/app/discover#/view/19f84090-4bbf-11ed-8b6a-fbdb491d4e24?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1h,to:now))&_a=(columns:!(message),filters:!(('$state':(store:appState),meta:(alias:!n,disabled:!t,index:'6bc68b30-4a01-11ed-8b6a-fbdb491d4e24',key:message,negate:!t,params:(query:NoCacheToArchive),type:phrase),query:(match_phrase:(message:NoCacheToArchive))),('$state':(store:appState),meta:(alias:!n,disabled:!f,index:'6bc68b30-4a01-11ed-8b6a-fbdb491d4e24',key:message,negate:!t,params:(query:'T:PRAGMATIC_WEB_SOCKET_TRANSFORMER,ST:TRANSFORM_PRAGMATIC_STATE'),type:phrase),query:(match_phrase:(message:'T:PRAGMATIC_WEB_SOCKET_TRANSFORMER,ST:TRANSFORM_PRAGMATIC_STATE'))),('$state':(store:appState),meta:(alias:!n,disabled:!f,index:'6bc68b30-4a01-11ed-8b6a-fbdb491d4e24',key:message,negate:!f,params:!(error),type:phrases),query:(bool:(minimum_should_match:1,should:!((match_phrase:(message:error))))))),grid:(),hideChart:!f,index:'6bc68b30-4a01-11ed-8b6a-fbdb491d4e24',interval:auto,query:(language:kuery,query:''),sort:!(!('@timestamp',asc)))",
            "name": "Gaming Feed Errors alert",
            "noDataState": "alerting",
            "notifications": [
              {
                "uid": "pagerduty1"
              }
            ]
          },
          "aliasColors": {},
          "bars": true,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Elasticsearch-ews-gaming-feed",
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "displayName": "ERRORS",
              "thresholds": {
                "mode": "absolute",
                "steps": []
              }
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 49
          },
          "hiddenSeries": false,
          "id": 20,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": true,
            "values": true
          },
          "lines": false,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "alias": "ews-gaming -feed",
              "bucketAggs": [
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "(Error) AND NOT \"T:PRAGMATIC_WEB_SOCKET_TRANSFORMER\" AND NOT \"ST:TRANSFORM_PRAGMATIC_STATE\" AND NOT \"NoCacheToArchive\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "gt",
              "value": 1000,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "GAMING FEED",
          "tooltip": {
            "shared": false,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "series",
            "name": null,
            "show": true,
            "values": [
              "total"
            ]
          },
          "yaxes": [
            {
              "$$hashKey": "object:991",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:992",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "alert": {
            "alertRuleTags": {},
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    200
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "1h",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "sum"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "1m",
            "frequency": "30sec",
            "handler": 1,
            "message": "https://kibana.kas-dr.kube-infra.com/app/discover#/view/d397be00-6b7b-11ed-917b-d705c3fd77ce?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1h,to:now))&_a=(columns:!(),filters:!(),grid:(),hideChart:!f,index:'53eab9a0-cb8a-11ec-97be-3b039cff46d8',interval:auto,query:(language:kuery,query:'%22%2Fapi%2Fgaming%2Fegt%22%20and%20request_time%20%3E%204'),sort:!(!('@timestamp',desc)))",
            "name": "REQUEST TIME EGT > 4 alert",
            "noDataState": "alerting",
            "notifications": [
              {
                "uid": "pagerduty1"
              }
            ]
          },
          "aliasColors": {},
          "bars": true,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Elastic-kas-nginx-plus-ingress",
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "thresholds": {
                "mode": "absolute",
                "steps": []
              }
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 57
          },
          "hiddenSeries": false,
          "id": 34,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": true,
            "values": true
          },
          "lines": false,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "\"/api/gaming/egt\" AND request_time: [4 TO *]",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "gt",
              "value": 200,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "REQUEST TIME EGT > 4",
          "tooltip": {
            "shared": false,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "series",
            "name": null,
            "show": true,
            "values": [
              "total"
            ]
          },
          "yaxes": [
            {
              "$$hashKey": "object:1151",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:1152",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "alert": {
            "alertRuleTags": {
              "Severity": "Critical"
            },
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    200
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "1h",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "sum"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "1m",
            "frequency": "30sec",
            "handler": 1,
            "message": "https://kibana.kas-dr.kube-infra.com/app/discover#/view/bb209ea0-6b7b-11ed-917b-d705c3fd77ce?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1h,to:now))&_a=(columns:!(),filters:!(),grid:(),hideChart:!f,index:'53eab9a0-cb8a-11ec-97be-3b039cff46d8',interval:auto,query:(language:kuery,query:'%22%2Fapi%2Fgaming%2Fegti%22%20and%20request_time%20%3E%204'),sort:!(!('@timestamp',desc)))",
            "name": "REQUEST TIME AMUSNET > 4 alert",
            "noDataState": "alerting",
            "notifications": [
              {
                "uid": "pagerduty1"
              }
            ]
          },
          "aliasColors": {},
          "bars": true,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Elastic-kas-nginx-plus-ingress",
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "thresholds": {
                "mode": "absolute",
                "steps": []
              }
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 57
          },
          "hiddenSeries": false,
          "id": 32,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": true,
            "values": true
          },
          "lines": false,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "\"/api/gaming/egti\" AND request_time: [4 TO *]",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "gt",
              "value": 200,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "REQUEST TIME AMUSNET > 4",
          "tooltip": {
            "shared": false,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "series",
            "name": null,
            "show": true,
            "values": [
              "total"
            ]
          },
          "yaxes": [
            {
              "$$hashKey": "object:1305",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:1306",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "alert": {
            "alertRuleTags": {},
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    1000
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "15m",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "sum"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "1m",
            "frequency": "30sec",
            "handler": 1,
            "message": "https://kibana.kas-dr.kube-infra.com/app/discover#/view/521af040-6b7b-11ed-917b-d705c3fd77ce?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-15m,to:now))&_a=(columns:!(message),filters:!(('$state':(store:appState),meta:(alias:!n,disabled:!f,index:'53eab9a0-cb8a-11ec-97be-3b039cff46d8',key:message,negate:!t,params:(query:%2Fapi%2Fcapi),type:phrase),query:(match_phrase:(message:%2Fapi%2Fcapi))),('$state':(store:appState),meta:(alias:!n,disabled:!f,index:'53eab9a0-cb8a-11ec-97be-3b039cff46d8',key:message,negate:!t,params:(query:%22%2Fapi%2Fpayment%22),type:phrase),query:(match_phrase:(message:%22%2Fapi%2Fpayment%22)))),grid:(),hideChart:!f,index:'53eab9a0-cb8a-11ec-97be-3b039cff46d8',interval:auto,query:(language:kuery,query:'%22%2Fapi%2F%22%20and%20request_time%20%3E%205%20and%20not%20http_host%20:%20%22%0Ainbet-retail-cashier-modern-api.egt-digital.com%0A%22%20and%20not%20%22gameState%22'),sort:!(!('@timestamp',desc)))",
            "name": "API Request time > 5 alert",
            "noDataState": "alerting",
            "notifications": [
              {
                "uid": "pagerduty1"
              }
            ]
          },
          "aliasColors": {},
          "bars": true,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Elastic-kas-nginx-plus-ingress",
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "thresholds": {
                "mode": "absolute",
                "steps": []
              },
              "unit": "none"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 65
          },
          "hiddenSeries": false,
          "id": 22,
          "interval": null,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": true,
            "values": true
          },
          "lines": false,
          "linewidth": 1,
          "maxDataPoints": null,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "alias": "nginx-plus-ingress*",
              "bucketAggs": [
                {
                  "field": "@timestamp",
                  "id": "1",
                  "settings": {
                    "interval": "auto",
                    "min_doc_count": "0",
                    "trimEdges": "0"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "message:\"/api/\" \nAND request_time:[5 TO *] \nAND NOT http_host:\"inbet-retail-cashier-modern-api.egt-digital.com\" \nAND NOT message:\"gameState\"\nAND NOT message:\"/api/capi\" \nAND NOT message:\"/api/payment\"\n",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "gt",
              "value": 1000,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "API Request time > 5",
          "tooltip": {
            "shared": false,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "series",
            "name": null,
            "show": true,
            "values": [
              "total"
            ]
          },
          "yaxes": [
            {
              "$$hashKey": "object:1477",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:1478",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "alert": {
            "alertRuleTags": {},
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    700
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "15m",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "sum"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "1m",
            "frequency": "30sec",
            "handler": 1,
            "message": "https://kibana.kas-dr.kube-infra.com/app/discover#/view/7bafed70-6b7b-11ed-917b-d705c3fd77ce?_g=(filters:!(),refreshInterval:(pause:!f,value:10000),time:(from:now-15m,to:now))&_a=(columns:!(message),filters:!(),grid:(),hideChart:!f,index:'53eab9a0-cb8a-11ec-97be-3b039cff46d8',interval:auto,query:(language:kuery,query:'%22%2Fapi%2Fpayment%22%20and%20request_time%20%3E%205'),sort:!(!('@timestamp',desc)))",
            "name": "PAYMENTS API REQUEST TIME > 5 alert",
            "noDataState": "alerting",
            "notifications": [
              {
                "uid": "pagerduty1"
              }
            ]
          },
          "aliasColors": {},
          "bars": true,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Elastic-kas-nginx-plus-ingress",
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "thresholds": {
                "mode": "absolute",
                "steps": []
              }
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 65
          },
          "hiddenSeries": false,
          "id": 30,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": true,
            "values": true
          },
          "lines": false,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "\"/api/payment\" AND request_time: [5 TO *]",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "gt",
              "value": 700,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "PAYMENTS API REQUEST TIME > 5",
          "tooltip": {
            "shared": false,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "series",
            "name": null,
            "show": true,
            "values": [
              "total"
            ]
          },
          "yaxes": [
            {
              "$$hashKey": "object:1655",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:1656",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "alert": {
            "alertRuleTags": {
              "Severity": "Critical"
            },
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    300
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "1h",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "sum"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "1m",
            "frequency": "30sec",
            "handler": 1,
            "message": "https://kibana-pl2.egt-digital.com/app/discover#/view/07aaaea0-4bba-11ed-8b6a-fbdb491d4e24?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1h,to:now))&_a=(columns:!(severity,message),filters:!(),grid:(),hideChart:!f,index:'2764c960-4a02-11ed-8b6a-fbdb491d4e24',interval:auto,query:(language:kuery,query:'%22L:Error%22'),sort:!(!('@timestamp',asc)))",
            "name": "Regulation Reporting Errors alert",
            "noDataState": "alerting",
            "notifications": [
              {
                "uid": "pagerduty1"
              }
            ]
          },
          "aliasColors": {},
          "bars": true,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Elastic-regulation-reporting",
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "displayName": "ERRORS",
              "thresholds": {
                "mode": "absolute",
                "steps": []
              }
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 73
          },
          "hiddenSeries": false,
          "id": 28,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": true,
            "values": true
          },
          "lines": false,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "alias": "",
              "bucketAggs": [
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "\"L:Error\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "gt",
              "value": 300,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "REGULATION REPORTING",
          "tooltip": {
            "shared": false,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "series",
            "name": null,
            "show": true,
            "values": [
              "total"
            ]
          },
          "yaxes": [
            {
              "$$hashKey": "object:2241",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:2242",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "alert": {
            "alertRuleTags": {
              "Severity": "Critical"
            },
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    650
                  ],
                  "type": "gt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "A",
                    "1h",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "sum"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "1m",
            "frequency": "30sec",
            "handler": 1,
            "message": "https://kibana-pl2.egt-digital.com/app/discover#/view/c20edba0-4bb9-11ed-8b6a-fbdb491d4e24?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1h,to:now))&_a=(columns:!(severity,message),filters:!(('$state':(store:appState),meta:(alias:!n,disabled:!f,index:a373e3d0-4a00-11ed-8b6a-fbdb491d4e24,key:message,negate:!t,params:(query:WBRS),type:phrase),query:(match_phrase:(message:WBRS)))),grid:(),hideChart:!f,index:'20e29950-506e-11ee-a7b2-256680d29499',interval:auto,query:(language:kuery,query:'(%22Error%22%20or%20%22L:Error%22%20or%20%22Exception%22)%20AND%20NOT%20%22AffiliateApiClientService%22%20AND%20NOT%20%22Utils%22%20AND%20NOT%0A%22GetPlayerTotalRevenue%22%20AND%20NOT%20%22Not%20correct%20timezone%20header%20timezone%3DNaN%22%20AND%20NOT%20%22Error%20resolving%20type%22%0AAND%20NOT%20%22Warning%22%20AND%20NOT%20%22Player%20notification%20channel%20is%20turned%20off%22%20AND%20NOT%20%22%2Fapi%2Fews-notification-service%2Fs2s%2Fplayer%22%20AND%20NOT%20%22Player%20has%20active%20restrictions%22%20AND%20NOT%20%22CustomHttpStatusCode%22%20and%20not%0A%22T:HeaderHashNetAppFilter%22'),sort:!(!('@timestamp',asc)))",
            "name": "CRM Errors alert",
            "noDataState": "alerting",
            "notifications": []
          },
          "aliasColors": {},
          "bars": true,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Elasticsearch-ews30-crm",
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "displayName": "ERRORS",
              "thresholds": {
                "mode": "absolute",
                "steps": []
              }
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 73
          },
          "hiddenSeries": false,
          "id": 18,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": true,
            "values": true
          },
          "lines": false,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "alias": "ews30-crm*",
              "bucketAggs": [
                {
                  "field": "@timestamp",
                  "id": "2",
                  "settings": {
                    "interval": "auto"
                  },
                  "type": "date_histogram"
                }
              ],
              "metrics": [
                {
                  "id": "1",
                  "type": "count"
                }
              ],
              "query": "(\"Error\" or \"L:Error\" or \"Exception\") AND NOT \"AffiliateApiClientService\" AND NOT \"Utils\" AND NOT\r\n\"GetPlayerTotalRevenue\" AND NOT \"Not correct timezone header timezone=NaN\" AND NOT \"Error resolving type\"\r\nAND NOT \"Warning\" AND NOT \"Player notification channel is turned off\" AND NOT \"/api/ews-notification-service/s2s/player\" AND NOT \"Player has active restrictions\" AND NOT \"CustomHttpStatusCode\" AND NOT\r\n\"T:HeaderHashNetAppFilter\" AND NOT \"WBRS\"",
              "refId": "A",
              "timeField": "@timestamp"
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "gt",
              "value": 650,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "CRM",
          "tooltip": {
            "shared": false,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "series",
            "name": null,
            "show": true,
            "values": [
              "total"
            ]
          },
          "yaxes": [
            {
              "$$hashKey": "object:2395",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:2396",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        }
      ],
      "refresh": "",
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": []
      },
      "time": {
        "from": "now-1h",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "browser",
      "title": "NOC Kibana Alerts",
      "uid": "9nVs5P7Kj",
      "version": 1
    }
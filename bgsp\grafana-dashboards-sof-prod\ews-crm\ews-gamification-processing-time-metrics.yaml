apiVersion: v1
kind: ConfigMap
metadata:
  name: gamification-processing-time-metrics-dashboard
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: ews-crm
data:
  gamification-processing-time-metrics-dashboard.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": {
              "type": "datasource",
              "uid": "grafana"
            },
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "iteration": 1750932732607,
      "links": [],
      "panels": [
        {
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {},
                "thresholdsStyle": {}
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "none"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 12,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "id": 33,
          "options": {
            "alertThreshold": true,
            "legend": {
              "calcs": [
                "mean",
                "lastNotNull",
                "max",
                "min"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "multi",
              "sort": "none"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum(process_num_threads{namespace=\"$namespace\"}\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$player_updates_pod_name\", workload_type=~\".*\"}) by (pod))",
              "interval": "",
              "legendFormat": "{{pod}}",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum(process_num_threads{namespace=\"$namespace\"}\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$stream_events_processor_pod_name\", workload_type=~\".*\"}) by (pod))",
              "hide": false,
              "interval": "",
              "legendFormat": "{{pod}}",
              "range": true,
              "refId": "B"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum(process_num_threads{namespace=\"$namespace\"}\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$aggregator_pod_name\", workload_type=~\".*\"}) by (pod))",
              "hide": false,
              "interval": "",
              "legendFormat": "{{pod}}",
              "range": true,
              "refId": "C"
            }
          ],
          "title": "Used CPU Threads Count",
          "type": "timeseries"
        },
        {
          "collapsed": false,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 12
          },
          "id": 52,
          "panels": [],
          "title": "Segments",
          "type": "row"
        },
        {
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {},
                "thresholdsStyle": {}
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "none"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "Stream Events Processor"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "dark-green",
                      "mode": "fixed"
                    }
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 13
          },
          "id": 49,
          "options": {
            "alertThreshold": true,
            "legend": {
              "calcs": [
                "mean",
                "lastNotNull",
                "max",
                "min"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "multi",
              "sort": "none"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "exemplar": false,
              "expr": "(sum(increase(get_segment_success_counter{namespace=\"$namespace\"}[1h])\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$stream_events_processor_pod_name\", workload_type=~\".*\"}) by (pod))",
              "hide": false,
              "instant": false,
              "interval": "",
              "legendFormat": "{{pod}}",
              "range": true,
              "refId": "B"
            }
          ],
          "title": "Get Segments Successfully Count",
          "type": "timeseries"
        },
        {
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {},
                "thresholdsStyle": {}
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "none"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "Stream Events Processor"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "dark-green",
                      "mode": "fixed"
                    }
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 13
          },
          "id": 50,
          "options": {
            "alertThreshold": true,
            "legend": {
              "calcs": [
                "mean",
                "lastNotNull",
                "max",
                "min"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "multi",
              "sort": "none"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum(increase(get_segment_failed_counter{namespace=\"$namespace\"}[1h])\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$stream_events_processor_pod_name\", workload_type=~\".*\"}) by (pod))",
              "hide": false,
              "interval": "",
              "legendFormat": "{{pod}} - {{SegmentId}}",
              "range": true,
              "refId": "B"
            }
          ],
          "title": "Get Segments Failed Count",
          "type": "timeseries"
        },
        {
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {},
                "thresholdsStyle": {}
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "s"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "Player Updates Processor Command Duration"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "dark-green",
                      "mode": "fixed"
                    }
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "Player Updates Processor Connection Create Time"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "super-light-green",
                      "mode": "fixed"
                    }
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 6,
            "y": 21
          },
          "id": 48,
          "options": {
            "alertThreshold": true,
            "legend": {
              "calcs": [
                "mean",
                "lastNotNull",
                "max",
                "min"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "multi",
              "sort": "none"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum(rate(get_segment_duration_sum{namespace=\"$namespace\"}[10m])/rate(get_segment_duration_count{namespace=\"$namespace\"}[10m])\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$stream_events_processor_pod_name\", workload_type=~\".*\"}) by (pod))",
              "interval": "",
              "legendFormat": "{{pod}}",
              "range": true,
              "refId": "A"
            }
          ],
          "title": "Get Segment Duration",
          "type": "timeseries"
        },
        {
          "collapsed": false,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 29
          },
          "id": 51,
          "panels": [],
          "title": "Database",
          "type": "row"
        },
        {
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {},
                "thresholdsStyle": {}
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "s"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "Player Updates Processor Command Duration"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "dark-green",
                      "mode": "fixed"
                    }
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "Player Updates Processor Connection Create Time"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "super-light-green",
                      "mode": "fixed"
                    }
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 30
          },
          "id": 44,
          "options": {
            "alertThreshold": true,
            "legend": {
              "calcs": [
                "mean",
                "lastNotNull",
                "max",
                "min"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "multi",
              "sort": "none"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(npgsql_db_client_connections_create_time_sum{namespace=\"$namespace\"}[10m])/rate(npgsql_db_client_connections_create_time_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$stream_events_processor_pod_name\", workload_type=~\".*\"}) by (pod))",
              "interval": "",
              "legendFormat": "{{pod}}",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(npgsql_db_client_connections_create_time_sum{namespace=\"$namespace\"}[10m])/rate(npgsql_db_client_connections_create_time_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$player_updates_pod_name\", workload_type=~\".*\"}) by (pod))",
              "hide": false,
              "interval": "",
              "legendFormat": "{{pod}}",
              "range": true,
              "refId": "B"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(npgsql_db_client_connections_create_time_sum{namespace=\"$namespace\"}[10m])/rate(npgsql_db_client_connections_create_time_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$aggregator_pod_name\", workload_type=~\".*\"}) by (pod))",
              "hide": false,
              "interval": "",
              "legendFormat": "{{pod}}",
              "range": true,
              "refId": "C"
            }
          ],
          "title": "DB Connection Open Duration",
          "type": "timeseries"
        },
        {
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {},
                "thresholdsStyle": {}
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "s"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "Player Updates Processor Command Duration"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "dark-green",
                      "mode": "fixed"
                    }
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "Player Updates Processor Connection Create Time"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "super-light-green",
                      "mode": "fixed"
                    }
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 30
          },
          "id": 43,
          "options": {
            "alertThreshold": true,
            "legend": {
              "calcs": [
                "mean",
                "lastNotNull",
                "max",
                "min"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "multi",
              "sort": "none"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(npgsql_db_client_commands_duration_sum{namespace=\"$namespace\"}[10m])/rate(npgsql_db_client_commands_duration_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$player_updates_pod_name\", workload_type=~\".*\"}) by (pod))",
              "interval": "",
              "legendFormat": "{{pod}}",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(npgsql_db_client_commands_duration_sum{namespace=\"$namespace\"}[10m])/rate(npgsql_db_client_commands_duration_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$stream_events_processor_pod_name\", workload_type=~\".*\"}) by (pod))",
              "hide": false,
              "interval": "",
              "legendFormat": "{{pod}}",
              "range": true,
              "refId": "B"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(npgsql_db_client_commands_duration_sum{namespace=\"$namespace\"}[10m])/rate(npgsql_db_client_commands_duration_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$aggregator_pod_name\", workload_type=~\".*\"}) by (pod))",
              "hide": false,
              "interval": "",
              "legendFormat": "{{pod}}",
              "range": true,
              "refId": "C"
            }
          ],
          "title": "DB Commands Duration",
          "type": "timeseries"
        },
        {
          "collapsed": false,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 38
          },
          "id": 42,
          "panels": [],
          "title": "Redis",
          "type": "row"
        },
        {
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {},
                "thresholdsStyle": {}
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "s"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "Player Updates Processor Command Duration"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "dark-green",
                      "mode": "fixed"
                    }
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "Player Updates Processor Connection Create Time"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "super-light-green",
                      "mode": "fixed"
                    }
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 39
          },
          "id": 45,
          "options": {
            "alertThreshold": true,
            "legend": {
              "calcs": [
                "mean",
                "lastNotNull",
                "max",
                "min"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "multi",
              "sort": "none"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(redis_player_lock_duration_sum{namespace=\"$namespace\"}[10m])/rate(redis_player_lock_duration_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$stream_events_processor_pod_name\", workload_type=~\".*\"}) by (pod))\r\n",
              "interval": "",
              "legendFormat": "{{pod}}",
              "range": true,
              "refId": "A"
            }
          ],
          "title": "Redis Player Lock Duration",
          "type": "timeseries"
        },
        {
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {},
                "thresholdsStyle": {}
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "s"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "Player Updates Processor Command Duration"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "dark-green",
                      "mode": "fixed"
                    }
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "Player Updates Processor Connection Create Time"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "super-light-green",
                      "mode": "fixed"
                    }
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 39
          },
          "id": 46,
          "options": {
            "alertThreshold": true,
            "legend": {
              "calcs": [
                "mean",
                "lastNotNull",
                "max",
                "min"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "multi",
              "sort": "none"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(\r\n  rate(redis_get_duration_sum{namespace=\"$namespace\"}[10m])\r\n  /\r\n  rate(redis_get_duration_count{namespace=\"$namespace\"}[10m])\r\n)\r\n* on (namespace, pod)\r\ngroup_left(workload, workload_type)\r\nnamespace_workload_pod:kube_pod_owner:relabel{\r\n  namespace=\"$namespace\", \r\n  workload=~\"$stream_events_processor_pod_name\", \r\n  workload_type=~\".*\"\r\n}\r\n",
              "interval": "",
              "legendFormat": "{{pod}} {{command}}",
              "range": true,
              "refId": "A"
            }
          ],
          "title": "Redis Query Duration",
          "type": "timeseries"
        },
        {
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {},
                "thresholdsStyle": {}
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "none"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "Stream Events Processor"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "dark-red",
                      "mode": "fixed"
                    }
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 6,
            "y": 47
          },
          "id": 47,
          "options": {
            "alertThreshold": true,
            "legend": {
              "calcs": [
                "mean",
                "lastNotNull",
                "max",
                "min"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "multi",
              "sort": "none"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((increase(redis_not_acquired_player_lock_counter{namespace=\"$namespace\"}[1h]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$stream_events_processor_pod_name\", workload_type=~\".*\"}) by (pod))\r\n\r\n",
              "hide": false,
              "interval": "",
              "legendFormat": "{{pod}}",
              "range": true,
              "refId": "B"
            }
          ],
          "title": "Redis Not Acquired Lock Count",
          "type": "timeseries"
        },
        {
          "collapsed": false,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 55
          },
          "id": 34,
          "panels": [],
          "title": "Stream Events Processor",
          "type": "row"
        },
        {
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {},
                "thresholdsStyle": {}
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "s"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "All Events In Batch"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "dark-green",
                      "mode": "fixed"
                    }
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "Single Player Events"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "super-light-green",
                      "mode": "fixed"
                    }
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 56
          },
          "id": 35,
          "options": {
            "alertThreshold": true,
            "legend": {
              "calcs": [
                "mean",
                "lastNotNull",
                "max",
                "min"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "multi",
              "sort": "none"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(stream_worker_processing_gamification_stream_event_sum{namespace=\"$namespace\"}[10m])/rate(stream_worker_processing_gamification_stream_event_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$stream_events_processor_pod_name\", workload_type=~\".*\"}) by (pod))",
              "interval": "",
              "legendFormat": "{{pod}} - Batch",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(stream_worker_processing_gamification_stream_events_single_player_sum{namespace=\"$namespace\"}[10m])/rate(stream_worker_processing_gamification_stream_events_single_player_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$stream_events_processor_pod_name\", workload_type=~\".*\"}) by (pod))",
              "hide": false,
              "interval": "",
              "legendFormat": "{{pod}} - Single Events",
              "range": true,
              "refId": "B"
            }
          ],
          "title": "Stream Events Processing",
          "type": "timeseries"
        },
        {
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {},
                "thresholdsStyle": {}
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "s"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "Single Standart Mission"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "super-light-yellow",
                      "mode": "fixed"
                    }
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "All Quest Missions"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "dark-blue",
                      "mode": "fixed"
                    }
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "Single Quest Mission"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "super-light-blue",
                      "mode": "fixed"
                    }
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "All Standard Missions"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "dark-yellow",
                      "mode": "fixed"
                    }
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "All Campaign Missions"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "dark-purple",
                      "mode": "fixed"
                    }
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "Single Campaign Mission"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "super-light-purple",
                      "mode": "fixed"
                    }
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 56
          },
          "id": 37,
          "options": {
            "alertThreshold": true,
            "legend": {
              "calcs": [
                "mean",
                "lastNotNull",
                "max",
                "min"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "multi",
              "sort": "none"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(player_missions_processing_sum{namespace=\"$namespace\"}[10m])/rate(player_missions_processing_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$stream_events_processor_pod_name\", workload_type=~\".*\"}) by (pod))",
              "interval": "",
              "legendFormat": "{{pod}} - All Standart Mission",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(player_mission_processing_sum{namespace=\"$namespace\"}[10m])/rate(player_mission_processing_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$stream_events_processor_pod_name\", workload_type=~\".*\"}) by (pod))",
              "hide": false,
              "instant": false,
              "legendFormat": "{{pod}} - Single Standart Mission",
              "range": true,
              "refId": "B"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(player_quest_missions_processing_sum{namespace=\"$namespace\"}[10m])/rate(player_quest_missions_processing_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$stream_events_processor_pod_name\", workload_type=~\".*\"}) by (pod))",
              "hide": false,
              "instant": false,
              "legendFormat": "{{pod}} - All Quest Missions",
              "range": true,
              "refId": "C"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(player_quest_mission_processing_sum{namespace=\"$namespace\"}[10m])/rate(player_quest_mission_processing_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$stream_events_processor_pod_name\", workload_type=~\".*\"}) by (pod))",
              "hide": false,
              "instant": false,
              "legendFormat": "{{pod}} - Single Quest Mission",
              "range": true,
              "refId": "D"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(player_campaign_missions_processing_sum{namespace=\"$namespace\"}[10m])/rate(player_campaign_missions_processing_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$stream_events_processor_pod_name\", workload_type=~\".*\"}) by (pod))",
              "hide": false,
              "instant": false,
              "legendFormat": "{{pod}} - All Campaign Missions",
              "range": true,
              "refId": "E"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(player_campaign_mission_processing_sum{namespace=\"$namespace\"}[10m])/rate(player_campaign_mission_processing_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$stream_events_processor_pod_name\", workload_type=~\".*\"}) by (pod))",
              "hide": false,
              "instant": false,
              "legendFormat": "{{pod}} - Single Campaign Mission",
              "range": true,
              "refId": "F"
            }
          ],
          "title": "Player Missions Processing",
          "type": "timeseries"
        },
        {
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {},
                "thresholdsStyle": {}
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "s"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 64
          },
          "id": 41,
          "options": {
            "alertThreshold": true,
            "legend": {
              "calcs": [
                "mean",
                "lastNotNull",
                "max",
                "min"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "multi",
              "sort": "none"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(stream_event_processing_sum{namespace=\"$namespace\"}[10m])/rate(stream_event_processing_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$stream_events_processor_pod_name\", workload_type=~\".*\"}) by (pod))",
              "interval": "",
              "legendFormat": "{{pod}}",
              "range": true,
              "refId": "A"
            }
          ],
          "title": "Awards And Notifications Processing",
          "type": "timeseries"
        },
        {
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {},
                "thresholdsStyle": {}
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "s"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 64
          },
          "id": 40,
          "options": {
            "alertThreshold": true,
            "legend": {
              "calcs": [
                "mean",
                "lastNotNull",
                "max",
                "min"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "multi",
              "sort": "none"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(player_event_to_coin_processing_sum{namespace=\"$namespace\"}[10m])/rate(player_event_to_coin_processing_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$stream_events_processor_pod_name\", workload_type=~\".*\"}) by (pod))",
              "interval": "",
              "legendFormat": "{{pod}}",
              "range": true,
              "refId": "A"
            }
          ],
          "title": "Events To Coins Processing",
          "type": "timeseries"
        },
        {
          "collapsed": false,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 72
          },
          "id": 22,
          "panels": [],
          "title": "Stream Events Aggregator",
          "type": "row"
        },
        {
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {},
                "thresholdsStyle": {}
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "s"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "Rake Events"
                },
                "properties": [
                  {
                    "id": "color",
                    "value": {
                      "fixedColor": "light-purple",
                      "mode": "fixed"
                    }
                  }
                ]
              }
            ]
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 7,
            "y": 73
          },
          "id": 26,
          "options": {
            "alertThreshold": true,
            "legend": {
              "calcs": [
                "mean",
                "lastNotNull",
                "max",
                "min"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "multi",
              "sort": "none"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(transaction_stream_worker_processing_all_threads_sum{namespace=\"$namespace\"}[10m])/rate(transaction_stream_worker_processing_all_threads_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$aggregator_pod_name\", workload_type=~\".*\"}) by (pod))",
              "interval": "",
              "legendFormat": "{{pod}} - Transaction Events",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(crm_stream_worker_processing_all_threads_sum{namespace=\"$namespace\"}[10m])/rate(crm_stream_worker_processing_all_threads_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$aggregator_pod_name\", workload_type=~\".*\"}) by (pod))",
              "hide": false,
              "interval": "",
              "legendFormat": "{{pod}} - Crm Events",
              "range": true,
              "refId": "B"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(bonuses_stream_worker_processing_all_threads_sum{namespace=\"$namespace\"}[10m])/rate(bonuses_stream_worker_processing_all_threads_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$aggregator_pod_name\", workload_type=~\".*\"}) by (pod))",
              "hide": false,
              "interval": "",
              "legendFormat": "{{pod}} - Bonus Events",
              "range": true,
              "refId": "C"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(rake_stream_worker_processing_all_threads_sum{namespace=\"$namespace\"}[10m])/rate(rake_stream_worker_processing_all_threads_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$aggregator_pod_name\", workload_type=~\".*\"}) by (pod))",
              "hide": false,
              "interval": "",
              "legendFormat": "{{pod}} - Rake Events",
              "range": true,
              "refId": "D"
            }
          ],
          "title": "Stream Events Processing",
          "type": "timeseries"
        },
        {
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {},
                "thresholdsStyle": {}
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "s"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 81
          },
          "id": 32,
          "options": {
            "alertThreshold": true,
            "legend": {
              "calcs": [
                "mean",
                "lastNotNull",
                "max",
                "min"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "multi",
              "sort": "none"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(stream_worker_processing_player_updates_sum{namespace=\"$namespace\"}[10m])/rate(stream_worker_processing_player_updates_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$player_updates_pod_name\", workload_type=~\".*\"}) by (pod))",
              "interval": "",
              "legendFormat": "{{pod}} - Batch",
              "range": true,
              "refId": "A"
            },
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum((rate(single_event_updates_sum{namespace=\"$namespace\"}[10m])/rate(single_event_updates_count{namespace=\"$namespace\"}[10m]))\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$player_updates_pod_name\", workload_type=~\".*\"}) by (pod))",
              "hide": false,
              "instant": false,
              "legendFormat": "{{pod}} Single Event",
              "range": true,
              "refId": "B"
            }
          ],
          "title": "Stream Events Processing",
          "type": "timeseries"
        },
        {
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false,
                  "viz": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": false,
                "stacking": {},
                "thresholdsStyle": {}
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "none"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 81
          },
          "id": 53,
          "options": {
            "alertThreshold": true,
            "legend": {
              "calcs": [
                "mean",
                "lastNotNull",
                "max",
                "min"
              ],
              "displayMode": "table",
              "placement": "bottom",
              "showLegend": true
            },
            "tooltip": {
              "mode": "multi",
              "sort": "none"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "11.3.0",
          "targets": [
            {
              "datasource": {
                "type": "prometheus",
                "uid": "prometheus"
              },
              "editorMode": "code",
              "expr": "(sum(increase(failed_save_player_updates_to_db_counter{namespace=\"$namespace\"}[1h])\r\n* on (namespace,pod)\r\ngroup_left(workload,workload_type) namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\", workload=~\"$player_updates_pod_name\", workload_type=~\".*\"}) by (pod))",
              "interval": "",
              "legendFormat": "{{pod}}",
              "range": true,
              "refId": "A"
            }
          ],
          "title": "Failed DB Save",
          "type": "timeseries"
        },
        {
          "collapsed": false,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 89
          },
          "id": 31,
          "panels": [],
          "title": "Player Updates Processor",
          "type": "row"
        }
      ],
      "refresh": "",
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": [
          {
            "allValue": null,
            "current": {
              "selected": false,
              "text": "ews-sof-prod",
              "value": "ews-sof-prod"
            },
            "datasource": null,
            "definition": "label_values(process_private_memory_bytes,namespace)",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": false,
            "label": "Namespace",
            "multi": false,
            "name": "namespace",
            "options": [],
            "query": {
              "query": "label_values(process_private_memory_bytes,namespace)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {
              "selected": false,
              "text": "ews-gamification-stream-events-aggregator",
              "value": "ews-gamification-stream-events-aggregator"
            },
            "datasource": null,
            "definition": "label_values(namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\"},workload)",
            "description": null,
            "error": null,
            "hide": 2,
            "includeAll": false,
            "label": "Stream Events Aggregator",
            "multi": false,
            "name": "aggregator_pod_name",
            "options": [],
            "query": {
              "query": "label_values(namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\"},workload)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "/ews-gamification-stream-events-aggregator.*/",
            "skipUrlSync": false,
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {
              "selected": false,
              "text": "ews-gamification-player-updates-processor",
              "value": "ews-gamification-player-updates-processor"
            },
            "datasource": null,
            "definition": "label_values(namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\"},workload)",
            "description": null,
            "error": null,
            "hide": 2,
            "includeAll": false,
            "label": "Player Updates",
            "multi": false,
            "name": "player_updates_pod_name",
            "options": [],
            "query": {
              "query": "label_values(namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\"},workload)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "/ews-gamification-player-updates-processor.*/",
            "skipUrlSync": false,
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {
              "selected": false,
              "text": "ews-gamification-stream-events-processor",
              "value": "ews-gamification-stream-events-processor"
            },
            "datasource": null,
            "definition": "label_values(namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\"},workload)",
            "description": null,
            "error": null,
            "hide": 2,
            "includeAll": false,
            "label": "Stream Events Processor",
            "multi": false,
            "name": "stream_events_processor_pod_name",
            "options": [],
            "query": {
              "query": "label_values(namespace_workload_pod:kube_pod_owner:relabel{namespace=\"$namespace\"},workload)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "/ews-gamification-stream-events-processor.*/",
            "skipUrlSync": false,
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          }
        ]
      },
      "time": {
        "from": "now-7d",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "Gamification Processing Time",
      "uid": "U9gfvud677htr4s23ewaty",
      "version": 2
    }
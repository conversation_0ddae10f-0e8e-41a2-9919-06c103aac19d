apiVersion: v1
kind: ConfigMap
metadata:
  name: player-registrations-dashboard-vvro
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: VVRO
data:
  player-registrations-dashboard-vvro.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": 620,
      "links": [],
      "panels": [
        {
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": []
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 11,
            "w": 5,
            "x": 0,
            "y": 0
          },
          "id": 3,
          "options": {
            "colorMode": "value",
            "graphMode": "area",
            "justifyMode": "auto",
            "orientation": "auto",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "text": {},
            "textMode": "auto"
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "format": "table",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  sum(count_id)\nFROM monitoring_player_registrations\nWHERE\n  $__timeFilter(create_date) and business_unit = 'VVRO'",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Total Player Registrations per Time Range",
          "type": "stat"
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "thresholds": {
                "mode": "absolute",
                "steps": []
              }
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 11,
            "w": 19,
            "x": 5,
            "y": 0
          },
          "hiddenSeries": false,
          "id": 2,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": false,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "time_series",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\ndate_trunc('hour', public.monitoring_player_registrations.create_date ) AS \"time\",\nbusiness_unit::text AS metric,\nsum(count_id)\nFROM monitoring_player_registrations\nWHERE\n$__timeFilter(create_date) and business_unit = 'VVRO'\ngroup by 1,2\nORDER BY 1,2",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Player Registrations per Time Range",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:334",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:335",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        }
      ],
      "refresh": "1m",
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": []
      },
      "time": {
        "from": "now-7d",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "Player Registrations",
      "uid": "y6Q0Xgsaegtvvro",
      "version": 2
    }
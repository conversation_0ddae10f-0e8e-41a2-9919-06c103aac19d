apiVersion: v1
kind: ConfigMap
metadata:
  name: ews-redis-stream-queue-prometheus-sourced-test-flux
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: infrastructure-dev
data:
  EWS_REDIS_STREAM_QUEUE_PROMETHEUS_SOURCED.json: |-
    {
      "annotations": {
          "list": [{
              "builtIn": 1,
              "datasource": "-- Grafana --",
              "enable": true,
              "hide": true,
              "iconColor": "rgba(0, 211, 255, 1)",
              "name": "Annotations & Alerts",
              "type": "dashboard"
          }]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": 78,
      "links": [],
      "panels": [{
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "fieldConfig": {
              "defaults": {
                  "color": {},
                  "custom": {},
                  "thresholds": {
                      "mode": "absolute",
                      "steps": []
                  }
              },
              "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
              "h": 20,
              "w": 24,
              "x": 0,
              "y": 0
          },
          "hiddenSeries": false,
          "id": 2,
          "legend": {
              "avg": false,
              "current": false,
              "max": false,
              "min": false,
              "show": true,
              "total": false,
              "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
              "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [{
                  "expr": "redis_stream_consumer_group_pending_messages_total{endpoint=\"http\", group=\"settlement-service\", job=\"chrnola-redis-exporter-feed-stream\", namespace=\"ews-dev\", service=\"chrnola-redis-exporter-feed-stream\", stream=\"feed-stream\"}",
                  "hide": false,
                  "instant": false,
                  "interval": "",
                  "legendFormat": "{{group}}",
                  "refId": "A"
              },
              {
                  "expr": "redis_stream_consumer_group_pending_messages_total{endpoint=\"http\", group=\"sports_offering_general\", job=\"chrnola-redis-exporter-feed-stream\", namespace=\"ews-dev\", service=\"chrnola-redis-exporter-feed-stream\", stream=\"feed-stream\"}",
                  "hide": false,
                  "instant": false,
                  "interval": "",
                  "legendFormat": "{{group}}",
                  "refId": "B"
              },
              {
                  "expr": "redis_stream_consumer_group_pending_messages_total{endpoint=\"http\", group=\"accounting-service\", job=\"chrnola-redis-exporter-accounting\", namespace=\"ews-dev\", service=\"chrnola-redis-exporter-accounting\", stream=\"strm:transactions\"}",
                  "hide": false,
                  "instant": false,
                  "interval": "",
                  "legendFormat": "{{group}}",
                  "refId": "C"
              }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "EWS-DEV settlement pending messages prometheus sourced",
          "tooltip": {
              "shared": true,
              "sort": 0,
              "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
              "buckets": null,
              "mode": "time",
              "name": null,
              "show": true,
              "values": []
          },
          "yaxes": [{
                  "format": "short",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": null,
                  "show": true
              },
              {
                  "format": "short",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": null,
                  "show": true
              }
          ],
          "yaxis": {
              "align": false,
              "alignLevel": null
          }
      }],
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
          "list": []
      },
      "time": {
          "from": "now-3h",
          "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "EWS-redis-stream-queue",
      "uid": "-4sRjklMk",
      "version": 5
     }

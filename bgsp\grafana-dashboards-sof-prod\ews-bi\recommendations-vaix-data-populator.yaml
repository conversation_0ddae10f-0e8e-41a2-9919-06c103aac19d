apiVersion: v1
kind: ConfigMap
metadata:
  name: recommendations-vaix-data-populator
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: ews-bi
data:
  recommendations-vaix-data-populator.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": 559,
      "iteration": 1711096083160,
      "links": [],
      "panels": [
        {
          "collapsed": true,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "id": 40,
          "panels": [
            {
              "datasource": null,
              "fieldConfig": {
                "defaults": {
                  "color": {
                    "mode": "palette-classic"
                  },
                  "custom": {
                    "axisLabel": "",
                    "axisPlacement": "auto",
                    "barAlignment": 0,
                    "drawStyle": "line",
                    "fillOpacity": 10,
                    "gradientMode": "none",
                    "hideFrom": {
                      "graph": false,
                      "legend": false,
                      "tooltip": false
                    },
                    "lineInterpolation": "linear",
                    "lineWidth": 1,
                    "pointSize": 5,
                    "scaleDistribution": {
                      "type": "linear"
                    },
                    "showPoints": "never",
                    "spanNulls": true
                  },
                  "mappings": [],
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "gridPos": {
                "h": 10,
                "w": 12,
                "x": 0,
                "y": 1
              },
              "id": 38,
              "options": {
                "graph": {},
                "legend": {
                  "calcs": [],
                  "displayMode": "list",
                  "placement": "bottom"
                },
                "tooltipOptions": {
                  "mode": "single"
                }
              },
              "pluginVersion": "7.4.2",
              "targets": [
                {
                  "expr": "sum by (Action) (rate(incoming_request_duration_sum{job=\"ews-recommendations-api\",namespace=\"$namespace\"}[5m])/rate(incoming_request_duration_count{job=\"ews-recommendations-api\",namespace=\"$namespace\"}[5m]))",
                  "interval": "",
                  "legendFormat": "{{Action}}",
                  "refId": "A"
                }
              ],
              "title": "Recommendations Api Duration",
              "type": "timeseries"
            },
            {
              "aliasColors": {
                "NewGames": "rgba(50, 116, 217, 0.5)",
                "RecommendedGames": "rgba(255, 120, 10, 0.5)",
                "SimilarGames": "rgba(242, 204, 12, 0.5)"
              },
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": null,
              "fieldConfig": {
                "defaults": {
                  "custom": {}
                },
                "overrides": []
              },
              "fill": 1,
              "fillGradient": 0,
              "gridPos": {
                "h": 10,
                "w": 12,
                "x": 12,
                "y": 1
              },
              "hiddenSeries": false,
              "id": 42,
              "legend": {
                "avg": false,
                "current": false,
                "max": false,
                "min": false,
                "show": true,
                "total": false,
                "values": false
              },
              "lines": true,
              "linewidth": 1,
              "nullPointMode": "null",
              "options": {
                "alertThreshold": true
              },
              "percentage": false,
              "pluginVersion": "7.4.2",
              "pointradius": 2,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "sum by (Type) (increase(valid_games_percent{job=\"ews-recommendations-internal-data-populator\",namespace=\"$namespace\"}[5m]))",
                  "interval": "",
                  "legendFormat": "{{Type}}",
                  "refId": "A"
                }
              ],
              "thresholds": [],
              "timeFrom": null,
              "timeRegions": [],
              "timeShift": null,
              "title": "Valid games percentage",
              "tooltip": {
                "shared": true,
                "sort": 0,
                "value_type": "individual"
              },
              "type": "graph",
              "xaxis": {
                "buckets": null,
                "mode": "time",
                "name": null,
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "$$hashKey": "object:96",
                  "format": "short",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": null,
                  "show": true
                },
                {
                  "$$hashKey": "object:97",
                  "format": "short",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": null,
                  "show": true
                }
              ],
              "yaxis": {
                "align": false,
                "alignLevel": null
              }
            }
          ],
          "title": "Stats",
          "type": "row"
        },
        {
          "collapsed": false,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 1
          },
          "id": 30,
          "panels": [],
          "title": "Recommendation Api",
          "type": "row"
        },
        {
          "aliasColors": {
            "Because You Played": "rgba(31, 96, 196, 0.8)",
            "I Feel Lucky": "rgba(224, 180, 0, 0.8)",
            "New Games": "rgba(55, 135, 45, 0.8)",
            "Recommended For You": "rgba(143, 59, 184, 0.8)",
            "errors": "rgba(196, 22, 42, 0.8)"
          },
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 6,
            "w": 12,
            "x": 0,
            "y": 2
          },
          "hiddenSeries": false,
          "id": 16,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "sum by (Category) (increase(categories_games_count{job=\"ews-recommendations-api\", namespace=\"$namespace\"}[5m]))",
              "interval": "",
              "legendFormat": "{{Category}}",
              "refId": "A"
            },
            {
              "expr": "sum by () (increase(count_errors{job=\"ews-recommendations-api\", namespace=\"$namespace\"}[5m]))",
              "hide": false,
              "interval": "",
              "legendFormat": "errors",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Recommendations Api Games Returned",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:661",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:662",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {
            "Because You Played": "rgba(31, 96, 196, 0.8)",
            "I Feel Lucky": "rgba(224, 180, 0, 0.8)",
            "New Games": "rgba(55, 135, 45, 0.8)",
            "Recommended For You": "rgba(143, 59, 184, 0.8)",
            "Trending Now": "rgba(250, 100, 0, 0.8)"
          },
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 6,
            "w": 6,
            "x": 12,
            "y": 2
          },
          "hiddenSeries": false,
          "id": 18,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "sum by (Category) (increase(categories_count{job=\"ews-recommendations-api\", namespace=\"$namespace\"}[5m]))",
              "interval": "",
              "legendFormat": "{{Category}}",
              "refId": "A"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Recommendations Api Categories Returned",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:748",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:749",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {
            "IBBG": "rgba(55, 135, 45, 0.8)",
            "WBBG": "rgba(86, 166, 75, 0.8)",
            "WBRO": "rgba(150, 217, 141, 0.8)",
            "WBTZ": "rgba(200, 242, 194, 0.8)"
          },
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fill": 2,
          "fillGradient": 0,
          "gridPos": {
            "h": 6,
            "w": 6,
            "x": 18,
            "y": 2
          },
          "hiddenSeries": false,
          "id": 20,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "increase(uploaded_categories{job=\"ews-recommendations-api\", namespace=\"$namespace\"}[5m])",
              "interval": "",
              "legendFormat": "{{BU}}",
              "refId": "A"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Recommendations Api Categories Uploaded",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:997",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:998",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "collapsed": false,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 8
          },
          "id": 36,
          "panels": [],
          "title": "Internal Data",
          "type": "row"
        },
        {
          "aliasColors": {
            "errors": "rgba(196, 22, 42, 0.8)",
            "file NewGames": "rgba(55, 135, 45, 0.8)",
            "file RecommendedGames": "rgba(143, 59, 184, 0.8)",
            "file SimilarGames": "rgba(31, 96, 196, 0.8)",
            "file TrendingGames": "rgba(250, 100, 0, 0.8)",
            "file_records NewGames": "rgba(86, 166, 75, 0.8)",
            "file_records RecommendedGames": "rgba(163, 82, 204, 0.8)",
            "file_records SimilarGames": "rgba(50, 116, 217, 0.8)",
            "file_records TrendingGames": "rgba(255, 120, 10, 0.8)",
            "redis NewGames": "rgba(86, 166, 75, 0.8)",
            "redis RecommendedGames": "rgba(163, 82, 204, 0.8)",
            "redis SimilarGames": "rgba(50, 116, 217, 0.8)",
            "redis TrendingGames": "rgba(255, 120, 10, 0.8)",
            "update_redis_records NewGames": "rgba(55, 135, 45, 0.8)",
            "update_redis_records RecommendedGames": "rgba(143, 59, 184, 0.8)",
            "update_redis_records SimilarGames": "rgba(31, 96, 196, 0.8)",
            "update_redis_records TrendingGames": "rgba(250, 100, 0, 0.8)"
          },
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 6,
            "w": 6,
            "x": 0,
            "y": 9
          },
          "hiddenSeries": false,
          "id": 12,
          "legend": {
            "alignAsTable": false,
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": null,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "increase(file_records{job=\"ews-recommendations-internal-data-populator\", namespace=\"$namespace\"}[5m])",
              "interval": "",
              "legendFormat": "file {{Type}}",
              "refId": "A"
            },
            {
              "expr": "increase(update_redis_records{job=\"ews-recommendations-internal-data-populator\", namespace=\"$namespace\"}[5m])",
              "hide": false,
              "interval": "",
              "legendFormat": "redis {{Type}}",
              "refId": "B"
            },
            {
              "expr": "increase(count_errors{job=\"ews-recommendations-internal-data-populator\", namespace=\"$namespace\"}[5m])",
              "hide": false,
              "interval": "",
              "legendFormat": "errors",
              "refId": "C"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Internal Data Populator Games",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:82",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:83",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {
            "backend": "rgba(196, 22, 42, 0.5)",
            "cached": "rgba(224, 180, 0, 0.5)",
            "processed": "rgba(55, 135, 45, 0.5)"
          },
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 6,
            "w": 6,
            "x": 6,
            "y": 9
          },
          "hiddenSeries": false,
          "id": 26,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "increase(favourite_games_to_redis{job=\"ews-recommendations-internal-data-populator\", namespace=\"$namespace\"}[5m])",
              "hide": false,
              "interval": "",
              "legendFormat": "processed",
              "refId": "C"
            },
            {
              "expr": "increase(favourite_games_from_file{job=\"ews-recommendations-internal-data-populator\", namespace=\"$namespace\"}[5m])",
              "interval": "",
              "legendFormat": "backend",
              "refId": "A"
            },
            {
              "expr": "increase(favourite_games_from_redis{job=\"ews-recommendations-internal-data-populator\", namespace=\"$namespace\"}[5m])",
              "hide": false,
              "interval": "",
              "legendFormat": "cached",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Internal Data Populator Favourite Games",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {
            "Backend Games": "rgba(196, 22, 42, 0.5)",
            "Cached Games": "rgba(224, 180, 0, 0.5)",
            "New Games": "rgba(55, 135, 45, 0.5)"
          },
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 6,
            "w": 6,
            "x": 12,
            "y": 9
          },
          "hiddenSeries": false,
          "id": 28,
          "legend": {
            "alignAsTable": false,
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "increase(count_backend_casino_games{job=\"ews-recommendations-internal-data-populator\", namespace=\"$namespace\"}[5m])",
              "interval": "",
              "legendFormat": "Backend Games",
              "refId": "A"
            },
            {
              "expr": "increase(count_cached_casino_games{job=\"ews-recommendations-internal-data-populator\", namespace=\"$namespace\"}[5m])",
              "hide": false,
              "interval": "",
              "legendFormat": "Cached Games",
              "refId": "B"
            },
            {
              "expr": "increase(count_new_casino_games{job=\"ews-recommendations-internal-data-populator\", namespace=\"$namespace\"}[5m])",
              "hide": false,
              "interval": "",
              "legendFormat": "New Games",
              "refId": "C"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Internal Data Populator Active Games",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {
            "clicks": "rgba(86, 166, 75, 0.8)",
            "impressions": "rgba(242, 204, 12, 0.8)"
          },
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "description": "",
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 6,
            "w": 6,
            "x": 18,
            "y": 9
          },
          "hiddenSeries": false,
          "id": 24,
          "legend": {
            "alignAsTable": false,
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "sum by () (increase(count_processed_clicks{job=\"ews-recommendations-vaix-interactions-populator\", namespace=\"$namespace\"}[5m]))",
              "interval": "",
              "legendFormat": "clicks",
              "refId": "A"
            },
            {
              "expr": "sum by () (increase(count_processed_impressions{job=\"ews-recommendations-vaix-interactions-populator\", namespace=\"$namespace\"}[5m]))",
              "hide": false,
              "interval": "",
              "legendFormat": "impressions",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Internal Data Populator Clicks and Impressions",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:98",
              "format": "short",
              "label": "",
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:99",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "collapsed": false,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 15
          },
          "id": 34,
          "panels": [],
          "title": "Error",
          "type": "row"
        },
        {
          "aliasColors": {
            "aaa": "rgba(255, 166, 176, 0.8)",
            "ews-recommendations-api": "rgba(196, 22, 42, 0.8)",
            "ews-recommendations-internal-data-populator": "light-red",
            "ews-recommendations-player-segments-updater": "rgba(224, 47, 68, 0.8)",
            "ews-recommendations-vaix-data-populator": "rgba(255, 166, 176, 0.8)",
            "ews-recommendations-vaix-interactions-populator": "rgba(237, 71, 90, 0.8)"
          },
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 5,
            "w": 24,
            "x": 0,
            "y": 16
          },
          "hiddenSeries": false,
          "id": 22,
          "legend": {
            "alignAsTable": false,
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "sum by (job) (increase(count_errors{job=\"ews-recommendations-vaix-data-populator\", namespace=\"$namespace\"}[5m]))",
              "hide": false,
              "interval": "",
              "legendFormat": "{{job}}",
              "refId": "D"
            },
            {
              "expr": "sum by (job) (increase(count_errors{job=\"ews-recommendations-vaix-interactions-populator\", namespace=\"$namespace\"}[5m]))",
              "hide": false,
              "interval": "",
              "legendFormat": "{{job}}",
              "refId": "E"
            },
            {
              "expr": "sum by (job) (increase(count_errors{job=\"ews-recommendations-internal-data-populator\", namespace=\"$namespace\"}[5m]))",
              "interval": "",
              "legendFormat": "{{job}}",
              "refId": "A"
            },
            {
              "expr": "sum by (job) (increase(count_errors{job=\"ews-recommendations-player-segments-updater\", namespace=\"$namespace\"}[5m]))",
              "hide": false,
              "interval": "",
              "legendFormat": "{{job}}",
              "refId": "B"
            },
            {
              "expr": "sum by (job) (increase(count_errors{job=\"ews-recommendations-api\", namespace=\"$namespace\"}[5m]))",
              "hide": false,
              "interval": "",
              "legendFormat": "{{job}}",
              "refId": "C"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Errors",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:1651",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:1652",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "collapsed": true,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 21
          },
          "id": 32,
          "panels": [
            {
              "aliasColors": {
                "backend": "rgba(196, 22, 42, 0.5)",
                "cached": "rgba(224, 180, 0, 0.5)",
                "games": "dark-green",
                "processed": "rgba(55, 135, 45, 0.5)"
              },
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": null,
              "fieldConfig": {
                "defaults": {
                  "custom": {}
                },
                "overrides": []
              },
              "fill": 1,
              "fillGradient": 0,
              "gridPos": {
                "h": 3,
                "w": 6,
                "x": 0,
                "y": 22
              },
              "hiddenSeries": false,
              "id": 4,
              "legend": {
                "alignAsTable": false,
                "avg": false,
                "current": false,
                "max": false,
                "min": false,
                "rightSide": true,
                "show": true,
                "total": false,
                "values": false
              },
              "lines": true,
              "linewidth": 1,
              "nullPointMode": "null",
              "options": {
                "alertThreshold": true
              },
              "percentage": false,
              "pluginVersion": "7.4.2",
              "pointradius": 2,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "increase(count_processed_casino_games{job=\"ews-recommendations-vaix-data-populator\", namespace=\"$namespace\"}[5m])",
                  "interval": "",
                  "legendFormat": "processed",
                  "refId": "A"
                },
                {
                  "expr": "increase(count_backend_casino_games{job=\"ews-recommendations-vaix-data-populator\", namespace=\"$namespace\"}[5m])",
                  "hide": false,
                  "interval": "",
                  "legendFormat": "backend",
                  "refId": "B"
                },
                {
                  "expr": "increase(count_cached_casino_games{job=\"ews-recommendations-vaix-data-populator\", namespace=\"$namespace\"}[5m])",
                  "hide": false,
                  "interval": "",
                  "legendFormat": "cached",
                  "refId": "C"
                }
              ],
              "thresholds": [],
              "timeFrom": null,
              "timeRegions": [],
              "timeShift": null,
              "title": "Vaix Data Populator Games",
              "tooltip": {
                "shared": true,
                "sort": 0,
                "value_type": "individual"
              },
              "type": "graph",
              "xaxis": {
                "buckets": null,
                "mode": "time",
                "name": null,
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "$$hashKey": "object:661",
                  "format": "short",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": null,
                  "show": true
                },
                {
                  "$$hashKey": "object:662",
                  "format": "short",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": null,
                  "show": true
                }
              ],
              "yaxis": {
                "align": false,
                "alignLevel": null
              }
            },
            {
              "aliasColors": {
                "IBBG": "rgba(31, 96, 196, 0.8)",
                "WBBG": "rgba(55, 135, 45, 0.8)"
              },
              "bars": false,
              "cacheTimeout": null,
              "dashLength": 10,
              "dashes": false,
              "datasource": null,
              "fieldConfig": {
                "defaults": {
                  "color": {},
                  "custom": {},
                  "thresholds": {
                    "mode": "absolute",
                    "steps": []
                  },
                  "unit": "short"
                },
                "overrides": []
              },
              "fill": 1,
              "fillGradient": 0,
              "gridPos": {
                "h": 3,
                "w": 6,
                "x": 6,
                "y": 22
              },
              "hiddenSeries": false,
              "id": 2,
              "interval": null,
              "legend": {
                "avg": false,
                "current": false,
                "max": false,
                "min": false,
                "rightSide": true,
                "show": true,
                "total": false,
                "values": false
              },
              "lines": true,
              "linewidth": 1,
              "links": [],
              "nullPointMode": "null",
              "options": {
                "alertThreshold": true
              },
              "percentage": false,
              "pluginVersion": "7.4.2",
              "pointradius": 2,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "$$hashKey": "object:225",
                  "aggregation": "Last",
                  "decimals": 2,
                  "displayAliasType": "Warning / Critical",
                  "displayType": "Regular",
                  "displayValueWithAlias": "Never",
                  "expr": "increase(count_processed_players{job=\"ews-recommendations-vaix-data-populator\", namespace=\"$namespace\"}[5m])",
                  "instant": false,
                  "interval": "",
                  "legendFormat": "{{BU}}",
                  "refId": "A",
                  "units": "none",
                  "valueHandler": "Number Threshold"
                }
              ],
              "thresholds": [],
              "timeFrom": null,
              "timeRegions": [],
              "timeShift": null,
              "title": "Vaix Data Populator Players",
              "tooltip": {
                "shared": true,
                "sort": 0,
                "value_type": "individual"
              },
              "type": "graph",
              "xaxis": {
                "buckets": null,
                "mode": "time",
                "name": null,
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "$$hashKey": "object:392",
                  "format": "short",
                  "label": "",
                  "logBase": 1,
                  "max": null,
                  "min": "0",
                  "show": true
                },
                {
                  "$$hashKey": "object:393",
                  "format": "short",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": "0",
                  "show": true
                }
              ],
              "yaxis": {
                "align": false,
                "alignLevel": null
              }
            },
            {
              "aliasColors": {
                "transactions": "dark-green"
              },
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": null,
              "fieldConfig": {
                "defaults": {
                  "custom": {}
                },
                "overrides": []
              },
              "fill": 1,
              "fillGradient": 0,
              "gridPos": {
                "h": 3,
                "w": 6,
                "x": 12,
                "y": 22
              },
              "hiddenSeries": false,
              "id": 6,
              "legend": {
                "avg": false,
                "current": false,
                "max": false,
                "min": false,
                "rightSide": true,
                "show": true,
                "total": false,
                "values": false
              },
              "lines": true,
              "linewidth": 1,
              "nullPointMode": "null",
              "options": {
                "alertThreshold": true
              },
              "percentage": false,
              "pluginVersion": "7.4.2",
              "pointradius": 2,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "increase(count_processed_casino_transactions{job=\"ews-recommendations-vaix-data-populator\", namespace=\"$namespace\"}[5m])",
                  "interval": "",
                  "legendFormat": "transactions",
                  "refId": "A"
                }
              ],
              "thresholds": [],
              "timeFrom": null,
              "timeRegions": [],
              "timeShift": null,
              "title": "Vaix Data Populator Transactions",
              "tooltip": {
                "shared": true,
                "sort": 0,
                "value_type": "individual"
              },
              "type": "graph",
              "xaxis": {
                "buckets": null,
                "mode": "time",
                "name": null,
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "$$hashKey": "object:1068",
                  "format": "short",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": null,
                  "show": true
                },
                {
                  "$$hashKey": "object:1069",
                  "format": "short",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": null,
                  "show": true
                }
              ],
              "yaxis": {
                "align": false,
                "alignLevel": null
              }
            },
            {
              "aliasColors": {
                "IBBG": "rgba(31, 96, 196, 0.8)",
                "WBBG": "rgba(55, 135, 45, 0.8)",
                "errors": "rgba(196, 22, 42, 0.8)",
                "segmentations": "rgba(224, 180, 0, 0.8)"
              },
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": null,
              "fieldConfig": {
                "defaults": {
                  "custom": {}
                },
                "overrides": []
              },
              "fill": 1,
              "fillGradient": 0,
              "gridPos": {
                "h": 3,
                "w": 6,
                "x": 18,
                "y": 22
              },
              "hiddenSeries": false,
              "id": 14,
              "legend": {
                "avg": false,
                "current": false,
                "max": false,
                "min": false,
                "rightSide": true,
                "show": true,
                "total": false,
                "values": false
              },
              "lines": true,
              "linewidth": 1,
              "nullPointMode": "null",
              "options": {
                "alertThreshold": true
              },
              "percentage": false,
              "pluginVersion": "7.4.2",
              "pointradius": 2,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "increase(count_segmented_players{job=\"ews-recommendations-player-segments-updater\", namespace=\"$namespace\"}[5m])",
                  "interval": "",
                  "legendFormat": "{{BU}}",
                  "refId": "A"
                },
                {
                  "expr": "increase(count_segmentations{job=\"ews-recommendations-player-segments-updater\", namespace=\"$namespace\"}[5m])",
                  "hide": false,
                  "interval": "",
                  "legendFormat": "segmentations",
                  "refId": "B"
                },
                {
                  "expr": "increase(count_errors{job=\"ews-recommendations-player-segments-updater\", namespace=\"$namespace\"}[5m])",
                  "hide": false,
                  "interval": "",
                  "legendFormat": "errors",
                  "refId": "C"
                }
              ],
              "thresholds": [],
              "timeFrom": null,
              "timeRegions": [],
              "timeShift": null,
              "title": "Player Segments Updater",
              "tooltip": {
                "shared": true,
                "sort": 0,
                "value_type": "individual"
              },
              "type": "graph",
              "xaxis": {
                "buckets": null,
                "mode": "time",
                "name": null,
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "$$hashKey": "object:329",
                  "format": "short",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": null,
                  "show": true
                },
                {
                  "$$hashKey": "object:330",
                  "format": "short",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": null,
                  "show": true
                }
              ],
              "yaxis": {
                "align": false,
                "alignLevel": null
              }
            }
          ],
          "title": "VAIX",
          "type": "row"
        }
      ],
      "refresh": "5s",
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": [
          {
            "allValue": null,
            "current": {
              "selected": false,
              "text": "ews-recommendations-internal-data-populator",
              "value": "ews-recommendations-internal-data-populator"
            },
            "datasource": null,
            "definition": "label_values(process_private_memory_bytes{},job)",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": false,
            "label": "Job",
            "multi": false,
            "name": "job",
            "options": [
              {
                "selected": false,
                "text": "ews-recommendations-api",
                "value": "ews-recommendations-api"
              },
              {
                "selected": true,
                "text": "ews-recommendations-internal-data-populator",
                "value": "ews-recommendations-internal-data-populator"
              },
              {
                "selected": false,
                "text": "ews-recommendations-player-segments-updater",
                "value": "ews-recommendations-player-segments-updater"
              },
              {
                "selected": false,
                "text": "ews-recommendations-vaix-data-populator",
                "value": "ews-recommendations-vaix-data-populator"
              }
            ],
            "query": {
              "query": "label_values(process_private_memory_bytes{},job)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 0,
            "regex": "/ews-recommendations.*/",
            "skipUrlSync": false,
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {
              "selected": false,
              "text": "ews-sof-prod",
              "value": "ews-sof-prod"
            },
            "datasource": null,
            "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": false,
            "label": "Namespace",
            "multi": false,
            "name": "namespace",
            "options": [
              {
                "isNone": true,
                "selected": false,
                "text": "None",
                "value": ""
              }
            ],
            "query": {
              "query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 0,
            "regex": "",
            "skipUrlSync": false,
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          }
        ]
      },
      "time": {
        "from": "now-1h",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "Recommendations VAIX data populator",
      "uid": "1oi2fgjtghytj85d",
      "version": 3
    }
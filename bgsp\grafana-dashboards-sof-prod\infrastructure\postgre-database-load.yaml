apiVersion: v1
kind: ConfigMap
metadata:
  name: postgre-database-load
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: infrastructure
data:
  postgre-database-load.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": 550,
      "links": [],
      "panels": [
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100_node_load1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-wh-2,node_load1"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100_node_load1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-prod-01,node_load1"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100_node_load1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-prod-02,node_load1"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100_node_load1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-wh-01,node_load1"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100_node_load1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-barman-01,node_load1"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100_node_load1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-db-audit,node_load1"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100_node_load5"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-wh-2,node_load5"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100_node_load5"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-prod-01,node_load5"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100_node_load5"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-prod-02,node_load5"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100_node_load5"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-wh-01,node_load5"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100_node_load5"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-barman-01,node_load5"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100_node_load5"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-db-audit,node_load5"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100_node_load15"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-wh-2,node_load15"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100_node_load15"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-prod-01,node_load15"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100_node_load15"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-prod-02,node_load15"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100_node_load15"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-wh-01,node_load15"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100_node_load15"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-barman-01,node_load15"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100_node_load15"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-db-audit,node_load15"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************8:9100_node_load1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "RemoteDB,node_load1"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************8:9100_node_load5"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "RemoteDB,node_load5"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************8:9100_node_load15"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "RemoteDB,node_load15"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100_node_load1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-wh-3,node_load1"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100_node_load5"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-wh-3,node_load5"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100_node_load15"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-wh-3,node_load15"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "**************:9100_node_load1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-db-standard,node_load1"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "**************:9100_node_load5"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-db-standard,node_load5"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "**************:9100_node_load15"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-db-standard,node_load15"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "**************:9100_node_load1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-db-standard-02,node_load1"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "**************:9100_node_load5"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-db-standard-02,node_load5"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "**************:9100_node_load15"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-db-standard-02,node_load15"
                  }
                ]
              }
            ]
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "hiddenSeries": false,
          "id": 2,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "node_load1{instance=~\"************:9100|************:9100|************:9100|************:9100|************:9100|************:9100|************8:9100|************:9100|**************:9100|**************:9100\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "{{instance}}_node_load1",
              "refId": "A"
            },
            {
              "expr": "node_load5{instance=~\"************:9100|************:9100|************:9100|************:9100|************:9100|************:9100|************8:9100|************:9100|**************:9100|**************:9100\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "{{instance}}_node_load5",
              "refId": "B"
            },
            {
              "expr": "node_load15{instance=~\"************:9100|************:9100|************:9100|************:9100|************:9100|************:9100|************8:9100|************:9100|**************:9100|**************:9100\"}",
              "hide": false,
              "interval": "",
              "legendFormat": "{{instance}}_node_load15",
              "refId": "C"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Database Load",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 24,
            "x": 0,
            "y": 8
          },
          "hiddenSeries": false,
          "id": 4,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "sort": "current",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "sum(pg_stat_activity_count{instance=\"************:9187\"})",
              "interval": "",
              "legendFormat": "bgsp-p-db-prod-01",
              "refId": "A"
            },
            {
              "expr": "sum(pg_stat_activity_count{instance=~\"************:9187\"})",
              "hide": false,
              "interval": "",
              "legendFormat": "bgsp-p-db-prod-02",
              "refId": "B"
            },
            {
              "expr": "sum(pg_stat_activity_count{instance=\"************:9187\"})",
              "hide": false,
              "interval": "",
              "legendFormat": "bgsp-p-db-wh-01",
              "refId": "C"
            },
            {
              "expr": "sum(pg_stat_activity_count{instance=\"************:9187\"})",
              "hide": false,
              "interval": "",
              "legendFormat": "bgsp-p-db-wh-2",
              "refId": "D"
            },
            {
              "expr": "sum(pg_stat_activity_count{instance=\"************:9187\"})",
              "hide": false,
              "interval": "",
              "legendFormat": "bgsp-p-db-barman-01",
              "refId": "E"
            },
            {
              "expr": "sum(pg_stat_activity_count{instance=\"************:9187\"})",
              "hide": false,
              "interval": "",
              "legendFormat": "bgsp-db-audit",
              "refId": "F"
            },
            {
              "expr": "sum(pg_stat_activity_count{instance=\"************8:9187\"})",
              "hide": false,
              "interval": "",
              "legendFormat": "RemoteDB",
              "refId": "G"
            },
            {
              "expr": "sum(pg_stat_activity_count{instance=\"************:9187\"})",
              "hide": false,
              "interval": "",
              "legendFormat": "bgsp-p-db-wh-3",
              "refId": "H"
            },
            {
              "expr": "sum(pg_stat_activity_count{instance=\"**************:9187\"})",
              "hide": false,
              "interval": "",
              "legendFormat": "bgsp-db-standard",
              "refId": "I"
            },
            {
              "expr": "sum(pg_stat_activity_count{instance=\"**************:9187\"})",
              "hide": false,
              "interval": "",
              "legendFormat": "bgsp-db-standard-02",
              "refId": "J"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Postgre active connections",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "dectbytes"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-wh-2"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-prod-01"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-prod-02"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-wh-01"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-db-audit"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************8:9100"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "RemoteDB"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "************:9100"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-p-db-wh-03"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "**************:9100"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-db-standard"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "**************:9100"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "bgsp-db-standard-02"
                  }
                ]
              }
            ]
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 7,
            "w": 24,
            "x": 0,
            "y": 16
          },
          "hiddenSeries": false,
          "id": 6,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 350,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "node_filesystem_free_bytes{job=\"postgres-sm\",mountpoint=\"/\",instance=~\"172.17.192.[3-9].*|************8:9100|************:9100\"} /1024/1024/1024/1024",
              "interval": "",
              "legendFormat": "{{instance}}",
              "refId": "A"
            },
            {
              "expr": "node_filesystem_free_bytes{job=\"postgres-standard-sm\",mountpoint=\"/\",instance=~\"172.17.192.*:9100\"}/1024/1024/1024/1024",
              "hide": false,
              "interval": "",
              "legendFormat": "{{instance}}",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Free disk in TB",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "dectbytes",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        }
      ],
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": []
      },
      "time": {
        "from": "now-3h",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "Database load metrics",
      "uid": "njJfghcHJthdjkj5",
      "version": 3
    }
apiVersion: v1
kind: ConfigMap
metadata:
  name: artifactory
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: infrastructure
data:
  artifactory.json: |
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "description": "Dashboard for JFrog Artifactory Prometheus exporter",
      "editable": true,
      "gnetId": 12113,
      "graphTooltip": 0,
      "iteration": 1645623998227,
      "links": [],
      "panels": [
        {
          "collapsed": false,
          "datasource": "Prometheus",
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "id": 367,
          "panels": [],
          "title": "Health",
          "type": "row"
        },
        {
          "cacheTimeout": null,
          "colorBackground": true,
          "colorValue": false,
          "colors": [
            "#299c46",
            "rgba(237, 129, 40, 0.89)",
            "#d44a3a"
          ],
          "datasource": "Prometheus",
          "decimals": 1,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "format": "s",
          "gauge": {
            "maxValue": 100,
            "minValue": 0,
            "show": false,
            "thresholdLabels": false,
            "thresholdMarkers": true
          },
          "gridPos": {
            "h": 2,
            "w": 24,
            "x": 0,
            "y": 1
          },
          "id": 384,
          "interval": null,
          "links": [],
          "mappingType": 1,
          "mappingTypes": [
            {
              "name": "value to text",
              "value": 1
            },
            {
              "name": "range to text",
              "value": 2
            }
          ],
          "maxDataPoints": 100,
          "nullPointMode": "connected",
          "nullText": null,
          "pluginVersion": "6.6.0",
          "postfix": "",
          "postfixFontSize": "50%",
          "prefix": "",
          "prefixFontSize": "50%",
          "rangeMaps": [
            {
              "from": "null",
              "text": "N/A",
              "to": "null"
            }
          ],
          "repeat": "instance",
          "repeatDirection": "h",
          "scopedVars": {
            "instance": {
              "selected": false,
              "text": "172.17.51.207:9531",
              "value": "172.17.51.207:9531"
            }
          },
          "sparkline": {
            "fillColor": "rgba(31, 118, 189, 0.18)",
            "full": false,
            "lineColor": "rgb(31, 120, 193)",
            "show": false,
            "ymax": null,
            "ymin": null
          },
          "tableColumn": "",
          "targets": [
            {
              "aggregation": "Last",
              "decimals": 2,
              "displayAliasType": "Warning / Critical",
              "displayType": "Regular",
              "displayValueWithAlias": "Never",
              "expr": "artifactory_system_license{instance=~\"$instance\"}",
              "format": "time_series",
              "group": {
                "alias": "",
                "name": "Status Checks",
                "url": ""
              },
              "instant": true,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "",
              "refId": "A",
              "units": "none",
              "valueHandler": "Number Threshold"
            }
          ],
          "thresholds": "",
          "timeFrom": null,
          "timeShift": null,
          "title": "License Expires in",
          "type": "singlestat",
          "valueFontSize": "80%",
          "valueMaps": [
            {
              "op": "=",
              "text": "N/A",
              "value": "null"
            }
          ],
          "valueName": "current"
        },
        {
          "cacheTimeout": null,
          "colorBackground": true,
          "colorValue": false,
          "colors": [
            "#d44a3a",
            "rgba(237, 129, 40, 0.89)",
            "#299c46"
          ],
          "datasource": "Prometheus",
          "decimals": null,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "format": "none",
          "gauge": {
            "maxValue": 100,
            "minValue": 0,
            "show": false,
            "thresholdLabels": false,
            "thresholdMarkers": true
          },
          "gridPos": {
            "h": 2,
            "w": 24,
            "x": 0,
            "y": 3
          },
          "id": 377,
          "interval": null,
          "links": [],
          "mappingType": 1,
          "mappingTypes": [
            {
              "name": "value to text",
              "value": 1
            },
            {
              "name": "range to text",
              "value": 2
            }
          ],
          "maxDataPoints": 100,
          "nullPointMode": "connected",
          "nullText": null,
          "postfix": "",
          "postfixFontSize": "50%",
          "prefix": "",
          "prefixFontSize": "50%",
          "rangeMaps": [
            {
              "from": "null",
              "text": "N/A",
              "to": "null"
            }
          ],
          "repeat": "instance",
          "repeatDirection": "h",
          "scopedVars": {
            "instance": {
              "selected": false,
              "text": "172.17.51.207:9531",
              "value": "172.17.51.207:9531"
            }
          },
          "sparkline": {
            "fillColor": "rgba(31, 118, 189, 0.18)",
            "full": false,
            "lineColor": "rgb(31, 120, 193)",
            "show": false
          },
          "tableColumn": "",
          "targets": [
            {
              "expr": "artifactory_system_healthy{instance=~\"$instance\"}",
              "format": "time_series",
              "instant": true,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "",
              "refId": "A"
            }
          ],
          "thresholds": "0,1",
          "timeFrom": null,
          "timeShift": null,
          "title": "Artifactory Health $instance ($version)",
          "type": "singlestat",
          "valueFontSize": "80%",
          "valueMaps": [
            {
              "op": "=",
              "text": "Healthy",
              "value": "1"
            },
            {
              "op": "=",
              "text": "DOWN",
              "value": "0"
            },
            {
              "op": "=",
              "text": "DOWN",
              "value": "null"
            }
          ],
          "valueName": "current"
        },
        {
          "cacheTimeout": null,
          "colorBackground": true,
          "colorValue": false,
          "colors": [
            "#d44a3a",
            "rgba(237, 129, 40, 0.89)",
            "#299c46"
          ],
          "datasource": "Prometheus",
          "decimals": null,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "format": "none",
          "gauge": {
            "maxValue": 100,
            "minValue": 0,
            "show": false,
            "thresholdLabels": false,
            "thresholdMarkers": true
          },
          "gridPos": {
            "h": 2,
            "w": 24,
            "x": 0,
            "y": 5
          },
          "id": 427,
          "interval": null,
          "links": [],
          "mappingType": 1,
          "mappingTypes": [
            {
              "name": "value to text",
              "value": 1
            },
            {
              "name": "range to text",
              "value": 2
            }
          ],
          "maxDataPoints": 100,
          "nullPointMode": "connected",
          "nullText": null,
          "postfix": "",
          "postfixFontSize": "50%",
          "prefix": "",
          "prefixFontSize": "50%",
          "rangeMaps": [
            {
              "from": "null",
              "text": "N/A",
              "to": "null"
            }
          ],
          "repeat": "instance",
          "repeatDirection": "h",
          "scopedVars": {
            "instance": {
              "selected": false,
              "text": "172.17.51.207:9531",
              "value": "172.17.51.207:9531"
            }
          },
          "sparkline": {
            "fillColor": "rgba(31, 118, 189, 0.18)",
            "full": false,
            "lineColor": "rgb(31, 120, 193)",
            "show": false
          },
          "tableColumn": "",
          "targets": [
            {
              "expr": "max(artifactory_up{instance=~\"$instance\"})",
              "format": "time_series",
              "instant": true,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "",
              "refId": "A"
            }
          ],
          "thresholds": "0,1",
          "timeFrom": null,
          "timeShift": null,
          "title": "Artifactory Exporter ($instance)",
          "type": "singlestat",
          "valueFontSize": "80%",
          "valueMaps": [
            {
              "op": "=",
              "text": "UP",
              "value": "1"
            },
            {
              "op": "=",
              "text": "DOWN",
              "value": "0"
            },
            {
              "op": "=",
              "text": "DOWN",
              "value": "null"
            }
          ],
          "valueName": "current"
        },
        {
          "datasource": "Prometheus",
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 7
          },
          "id": 334,
          "title": "Overviewe",
          "type": "row"
        },
        {
          "cacheTimeout": null,
          "datasource": "Prometheus",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "#d44a3a",
                    "value": null
                  },
                  {
                    "color": "rgba(237, 129, 40, 0.89)",
                    "value": 0
                  },
                  {
                    "color": "#299c46",
                    "value": 1
                  }
                ]
              },
              "unit": "decbytes"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 4,
            "w": 5,
            "x": 7,
            "y": 8
          },
          "id": 356,
          "links": [],
          "options": {
            "colorMode": "value",
            "graphMode": "area",
            "justifyMode": "auto",
            "orientation": "horizontal",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "text": {},
            "textMode": "auto"
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "expr": "artifactory_storage_filestore_used_bytes",
              "format": "time_series",
              "instant": false,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "",
              "refId": "A"
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Filestore Used Space",
          "transparent": true,
          "type": "stat"
        },
        {
          "cacheTimeout": null,
          "datasource": "Prometheus",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "decimals": 1,
              "displayName": "",
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "semi-dark-green",
                    "value": null
                  }
                ]
              },
              "unit": "decbytes"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 4,
            "w": 5,
            "x": 12,
            "y": 8
          },
          "id": 415,
          "links": [],
          "options": {
            "colorMode": "value",
            "graphMode": "area",
            "justifyMode": "auto",
            "orientation": "horizontal",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "text": {},
            "textMode": "auto"
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "expr": "max(artifactory_storage_artifacts_size_bytes)",
              "format": "time_series",
              "instant": false,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "",
              "refId": "A"
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Total Size of Artifacts",
          "transparent": true,
          "type": "stat"
        },
        {
          "cacheTimeout": null,
          "datasource": "Prometheus",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "#d44a3a",
                    "value": null
                  },
                  {
                    "color": "rgba(237, 129, 40, 0.89)",
                    "value": 0
                  },
                  {
                    "color": "#299c46",
                    "value": 1
                  }
                ]
              },
              "unit": "decgbytes"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 2,
            "w": 5,
            "x": 7,
            "y": 12
          },
          "id": 343,
          "interval": null,
          "links": [],
          "maxDataPoints": 100,
          "options": {
            "colorMode": "value",
            "graphMode": "area",
            "justifyMode": "auto",
            "orientation": "horizontal",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "text": {},
            "textMode": "auto"
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "expr": "(artifactory_storage_filestore_free_bytes * 1e-9) ",
              "format": "time_series",
              "instant": true,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "",
              "refId": "A"
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Filestore Free Space",
          "type": "stat"
        },
        {
          "cacheTimeout": null,
          "datasource": "Prometheus",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "decimals": 1,
              "displayName": "",
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "#d44a3a",
                    "value": null
                  },
                  {
                    "color": "rgba(237, 129, 40, 0.89)",
                    "value": 0
                  },
                  {
                    "color": "#299c46",
                    "value": 1
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 4,
            "w": 5,
            "x": 12,
            "y": 12
          },
          "id": 414,
          "links": [],
          "options": {
            "colorMode": "value",
            "graphMode": "area",
            "justifyMode": "auto",
            "orientation": "horizontal",
            "reduceOptions": {
              "calcs": [
                "lastNotNull"
              ],
              "fields": "",
              "values": false
            },
            "text": {},
            "textMode": "auto"
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "expr": "max(artifactory_storage_artifacts)",
              "format": "time_series",
              "instant": false,
              "interval": "",
              "intervalFactor": 1,
              "legendFormat": "",
              "refId": "A"
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Total Number of Artifacts",
          "type": "stat"
        },
        {
          "cacheTimeout": null,
          "colorBackground": false,
          "colorValue": true,
          "colors": [
            "#d44a3a",
            "rgba(237, 129, 40, 0.89)",
            "#299c46"
          ],
          "datasource": "Prometheus",
          "decimals": null,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "format": "none",
          "gauge": {
            "maxValue": 100,
            "minValue": 0,
            "show": false,
            "thresholdLabels": false,
            "thresholdMarkers": true
          },
          "gridPos": {
            "h": 2,
            "w": 5,
            "x": 7,
            "y": 14
          },
          "id": 361,
          "interval": null,
          "links": [],
          "mappingType": 1,
          "mappingTypes": [
            {
              "name": "value to text",
              "value": 1
            },
            {
              "name": "range to text",
              "value": 2
            }
          ],
          "maxDataPoints": 100,
          "nullPointMode": "connected",
          "nullText": null,
          "postfix": "",
          "postfixFontSize": "50%",
          "prefix": "",
          "prefixFontSize": "50%",
          "rangeMaps": [
            {
              "from": "null",
              "text": "N/A",
              "to": "null"
            }
          ],
          "sparkline": {
            "fillColor": "rgba(31, 118, 189, 0.18)",
            "full": false,
            "lineColor": "rgb(31, 120, 193)",
            "show": false
          },
          "tableColumn": "",
          "targets": [
            {
              "expr": "max(artifactory_security_users{realm=\"internal\"})",
              "format": "time_series",
              "instant": true,
              "intervalFactor": 1,
              "refId": "A"
            }
          ],
          "thresholds": "0,1",
          "timeFrom": null,
          "timeShift": null,
          "title": "Number of Internal Users",
          "type": "singlestat",
          "valueFontSize": "80%",
          "valueMaps": [],
          "valueName": "current"
        },
        {
          "columns": [
            {
              "text": "Avg",
              "value": "avg"
            }
          ],
          "datasource": "Prometheus",
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fontSize": "100%",
          "gridPos": {
            "h": 13,
            "w": 24,
            "x": 0,
            "y": 16
          },
          "id": 338,
          "pageSize": null,
          "showHeader": true,
          "sort": {
            "col": 8,
            "desc": true
          },
          "styles": [
            {
              "alias": "",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": 2,
              "mappingType": 1,
              "pattern": "Time",
              "thresholds": [],
              "type": "hidden",
              "unit": "short"
            },
            {
              "alias": "Files",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": 0,
              "mappingType": 1,
              "pattern": "Value #A",
              "thresholds": [],
              "type": "number",
              "unit": "none"
            },
            {
              "alias": "Folders",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": 0,
              "mappingType": 1,
              "pattern": "Value #B",
              "thresholds": [],
              "type": "number",
              "unit": "none"
            },
            {
              "alias": "Items",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": 0,
              "mappingType": 1,
              "pattern": "Value #C",
              "thresholds": [],
              "type": "number",
              "unit": "none"
            },
            {
              "alias": "Used Space",
              "align": "auto",
              "colorMode": null,
              "colors": [
                "rgba(245, 54, 54, 0.9)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(50, 172, 45, 0.97)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": 2,
              "mappingType": 1,
              "pattern": "Value #D",
              "thresholds": [],
              "type": "number",
              "unit": "decbytes"
            },
            {
              "alias": "Percentage",
              "align": "auto",
              "colorMode": "cell",
              "colors": [
                "rgba(50, 172, 45, 0.97)",
                "rgba(237, 129, 40, 0.89)",
                "rgba(245, 54, 54, 0.9)"
              ],
              "dateFormat": "YYYY-MM-DD HH:mm:ss",
              "decimals": 1,
              "mappingType": 1,
              "pattern": "Value #E",
              "thresholds": [],
              "type": "number",
              "unit": "percent"
            }
          ],
          "targets": [
            {
              "expr": "max(artifactory_storage_repo_files) by (package_type,type,name)",
              "format": "table",
              "instant": true,
              "refId": "A"
            },
            {
              "expr": "max(artifactory_storage_repo_folders) by (package_type,type,name)",
              "format": "table",
              "instant": true,
              "refId": "B"
            },
            {
              "expr": "max(artifactory_storage_repo_items) by (package_type,type,name)",
              "format": "table",
              "instant": true,
              "refId": "C"
            },
            {
              "expr": "max(artifactory_storage_repo_used_bytes) by (package_type,type,name)",
              "format": "table",
              "instant": true,
              "interval": "",
              "legendFormat": "",
              "refId": "D"
            },
            {
              "expr": "max(artifactory_storage_repo_percentage) by (package_type,type,name)",
              "format": "table",
              "instant": true,
              "refId": "E"
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Repositories Summary",
          "transform": "table",
          "type": "table-old"
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Prometheus",
          "decimals": 0,
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 2,
          "fillGradient": 8,
          "gridPos": {
            "h": 10,
            "w": 24,
            "x": 0,
            "y": 29
          },
          "hiddenSeries": false,
          "id": 403,
          "interval": "",
          "legend": {
            "alignAsTable": true,
            "avg": true,
            "current": false,
            "hideEmpty": false,
            "hideZero": false,
            "max": true,
            "min": true,
            "rightSide": true,
            "show": true,
            "sideWidth": 0,
            "total": true,
            "values": true
          },
          "lines": true,
          "linewidth": 2,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": true,
          "steppedLine": false,
          "targets": [
            {
              "expr": "sum(max(artifactory_artifacts_created_1m{type=\"local\",name!~\"artifactory-build-info|auto-trashcan\"} > 0) by (name))",
              "format": "time_series",
              "interval": "",
              "legendFormat": "created",
              "refId": "A"
            },
            {
              "expr": "sum(max(artifactory_artifacts_downloaded_1m{type=\"local\",name!~\"artifactory-build-info|auto-trashcan\"} > 0) by (name))",
              "format": "time_series",
              "interval": "",
              "legendFormat": "downloaded",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Total Created and Downloaded Artifacts/Minute",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "decimals": 0,
              "format": "none",
              "label": "",
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "decimals": 0,
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": true,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Prometheus",
          "decimals": 0,
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 2,
          "fillGradient": 8,
          "gridPos": {
            "h": 9,
            "w": 24,
            "x": 0,
            "y": 39
          },
          "hiddenSeries": false,
          "id": 404,
          "interval": "",
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": false,
            "hideEmpty": false,
            "hideZero": false,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 0,
            "sort": "total",
            "sortDesc": true,
            "total": true,
            "values": true
          },
          "lines": false,
          "linewidth": 2,
          "nullPointMode": "null as zero",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "max(artifactory_artifacts_downloaded_1m{type=\"local\",name!~\"artifactory-build-info|auto-trashcan\"} > 0) by (name)",
              "format": "time_series",
              "interval": "",
              "legendFormat": "{{name}}",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Downloaded Artifacts/Minute per repo",
          "tooltip": {
            "shared": true,
            "sort": 2,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "decimals": 0,
              "format": "none",
              "label": "",
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "decimals": 0,
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": true,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Prometheus",
          "decimals": 0,
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "links": []
            },
            "overrides": []
          },
          "fill": 2,
          "fillGradient": 8,
          "gridPos": {
            "h": 9,
            "w": 24,
            "x": 0,
            "y": 48
          },
          "hiddenSeries": false,
          "id": 405,
          "interval": "",
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": false,
            "hideEmpty": false,
            "hideZero": false,
            "max": true,
            "min": false,
            "rightSide": true,
            "show": true,
            "sideWidth": 0,
            "sort": "total",
            "sortDesc": true,
            "total": true,
            "values": true
          },
          "lines": false,
          "linewidth": 2,
          "nullPointMode": "null as zero",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "max(artifactory_artifacts_created_1m{type=\"local\",name!~\"artifactory-build-info|auto-trashcan\"} > 0) by (name)",
              "format": "time_series",
              "interval": "",
              "legendFormat": "{{name}}",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Created Artifacts/Minute per repo",
          "tooltip": {
            "shared": true,
            "sort": 2,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "decimals": 0,
              "format": "none",
              "label": "",
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "decimals": 0,
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        }
      ],
      "refresh": "5s",
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": [
          {
            "allValue": "",
            "current": {
              "selected": false,
              "text": "All",
              "value": "$__all"
            },
            "datasource": "Prometheus",
            "definition": "label_values(artifactory_system_license,instance)",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": true,
            "label": "instance",
            "multi": false,
            "name": "instance",
            "options": [],
            "query": {
              "query": "label_values(artifactory_system_license,instance)",
              "refId": "Prometheus-instance-Variable-Query"
            },
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": ".+",
            "current": {
              "selected": true,
              "text": [
                "All"
              ],
              "value": [
                "$__all"
              ]
            },
            "datasource": "Prometheus",
            "definition": "label_values(artifactory_system_version{instance=~\"$instance\"},version)",
            "description": null,
            "error": null,
            "hide": 2,
            "includeAll": true,
            "label": "version",
            "multi": true,
            "name": "version",
            "options": [],
            "query": {
              "query": "label_values(artifactory_system_version{instance=~\"$instance\"},version)",
              "refId": "Prometheus-version-Variable-Query"
            },
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          }
        ]
      },
      "time": {
        "from": "now-5m",
        "to": "now"
      },
      "timepicker": {
        "refresh_intervals": [
          "5s",
          "10s",
          "30s",
          "1m",
          "5m",
          "15m",
          "30m",
          "1h",
          "2h",
          "1d"
        ],
        "time_options": [
          "5m",
          "15m",
          "1h",
          "6h",
          "12h",
          "24h",
          "2d",
          "7d",
          "30d"
        ]
      },
      "timezone": "",
      "title": "Artifactory Prometheus Exporter",
      "uid": "4446wo4g-qWzggaxc",
      "version": 3
    }
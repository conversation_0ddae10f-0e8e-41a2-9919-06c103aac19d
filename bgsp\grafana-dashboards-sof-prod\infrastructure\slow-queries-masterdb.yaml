apiVersion: v1
kind: ConfigMap
metadata:
  name: slow-queries-masterdb
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: infrastructure
data:
  slow-queries-masterdb.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": 1915,
      "iteration": 1669706012068,
      "links": [],
      "panels": [
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "PostgreSQL-postgres",
          "fieldConfig": {
            "defaults": {
              "color": {},
              "custom": {},
              "thresholds": {
                "mode": "absolute",
                "steps": []
              },
              "unit": "short"
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 12,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "hiddenSeries": false,
          "id": 7,
          "legend": {
            "alignAsTable": true,
            "avg": false,
            "current": true,
            "max": false,
            "min": false,
            "rightSide": true,
            "show": true,
            "sort": "current",
            "sortDesc": true,
            "total": true,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "time_series",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n    date::date                                        AS time,\n    COALESCE(username, 'n/a')                   AS user_name ,\n    slow_count                                  AS slow_count\nFROM\n    sqlog.user_stat\nWHERE \n  $__timeFilter(date)\nORDER BY\n1 DESC, 3 DESC;\n\n",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "value"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "timeColumn": "time",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Slow queries MasterDB",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:43",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:44",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        }
      ],
      "refresh": "",
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": [
          {
            "allValue": null,
            "current": {
              "selected": false,
              "text": "************:9100",
              "value": "************:9100"
            },
            "datasource": null,
            "definition": "label_values(node_time_seconds{job=\"$job\"},instance)",
            "description": null,
            "error": null,
            "hide": 2,
            "includeAll": false,
            "label": "Host",
            "multi": false,
            "name": "host",
            "options": [
              {
                "selected": true,
                "text": "************:9100",
                "value": "************:9100"
              }
            ],
            "query": {
              "query": "label_values(node_time_seconds{job=\"$job\"},instance)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 0,
            "regex": "/************.*/",
            "skipUrlSync": false,
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {
              "selected": false,
              "text": "postgres-sm",
              "value": "postgres-sm"
            },
            "datasource": null,
            "definition": "label_values(node_boot_time_seconds,job)",
            "description": null,
            "error": null,
            "hide": 2,
            "includeAll": false,
            "label": "Job",
            "multi": false,
            "name": "job",
            "options": [
              {
                "selected": true,
                "text": "postgres-sm",
                "value": "postgres-sm"
              }
            ],
            "query": {
              "query": "label_values(node_boot_time_seconds,job)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 0,
            "regex": "/(postgres.*)/",
            "skipUrlSync": false,
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          }
        ]
      },
      "time": {
        "from": "now-7d",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "Slow queries MasterDB",
      "uid": "ag01PIzVkdfcvnbf",
      "version": 1
    }
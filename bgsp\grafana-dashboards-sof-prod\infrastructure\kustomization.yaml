apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
- postgres-experimental
- postgres-producers-down.yaml
- rabbitmq-overview-devops.yaml
- redis-exporter-dashboard.yaml
- redis-pending-msg.yaml
- redis-vm-hosts-dashboard.yaml
- jvm-micrometer.yaml
- host-metrics-dashboard.yaml
- redis-memory-utilization.yaml
- postgresql-database.yaml
- x509-certificate-exporter-dashboard.yaml
- strimzi-kafka-exporter.yaml
- strimzi-kafka.yaml
#- ews-cache-populator.yaml
- barman-exporter-dashboard.yaml
- nginx-ingress-controller-sr.yaml
- nginx-api-ingress-controller-le.yaml
- nginx-game-provider-ingress-controller-le.yaml
- redis-stream-delay.yaml
- aggregations-alerts.yaml
- nra-service-failure.yaml
- data-expiry.yaml
- egti-slowapi-requests.yaml
- delay-postgre-replica.yaml
- casino-wallets.yaml
- vsphere-resource-consumption.yaml
- redis-DLT-messages.yaml
- artifactory-dashboard.yaml
- wbbg
- wbro
- ibbg
- wnrs
- smbg
- twmk
- bet365
- bhbg
- vvro
- mbrort
- mbro
- fbro
- lsro
- ftmx
- player-activity-total.yaml
- wal-postgre-files.yaml
- gaming-memory-super-dashboard.yaml
- slow-queries-masterdb.yaml
- average-usage-per-transaction.yaml
- pg-accounting-transactions-statistics.yaml
- pg-bloat-tables-masterdb.yaml
#- grafana-3rd-test.yaml
- coredns.yaml
- cloudflare.yaml
#- sport-betting-activity-alerting.yaml
- go-processes.yaml
- vsphere-cpu-resource-consumption-by-vm.yaml
- vsphere-memory-resource-consumption-by-vm.yaml
- pg-aggregation-executions.yaml
- pg-replication-slots.yaml
- vsphere-disk-writes-per-cluster.yaml
- postgre-database-load.yaml
- scylla

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: grafana-pvc-3rd-prod
  namespace: ews-sof-prod
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 2Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: grafana-3rd-test
  name: grafana-3rd-test
  namespace: ews-sof-prod
spec:
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: grafana-3rd-test
  template:
    metadata:
      labels:
        app: grafana-3rd-test
    spec:
      securityContext:
        fsGroup: 472
        supplementalGroups:
          - 0
      containers:
        - name: grafana
          image: grafana/grafana:8.4.4
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
              name: http-grafana
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /robots.txt
              port: 3000
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 2
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            tcpSocket:
              port: 3000
            timeoutSeconds: 1
          resources:
            requests:
              cpu: 250m
              memory: 750Mi
          volumeMounts:
            - mountPath: /var/lib/grafana
              name: grafana-pv-2
            - mountPath: /etc/grafana/
              name: grafana-config
      volumes:
        - name: grafana-pv-2
          persistentVolumeClaim:
            claimName: grafana-pvc-3rd-prod
        - name: grafana-config
          configMap:
            name: grafana-ini-2
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-ini-2
  namespace: ews-sof-prod
  annotations:
    reloader.stakater.com/match: "true"
data:
  grafana.ini: |
    [log]
      mode = console

    [security]
      allow_embedding = true

    [server]
      serve_from_sub_path = true
      domain = grafana-3rd-test.sof1-prod.kube-experts.com
      root_url = %(protocol)s://%(domain)s:%(http_port)s/grafana/

    #[live]
      # max_connections to Grafana Live WebSocket endpoint per Grafana server instance. See Grafana Live docs
      # if you are planning to make it higher than default 100 since this can require some OS and infrastructure
      # tuning. 0 disables Live, -1 means unlimited connections.
      #max_connections = -1
      # allowed_origins is a comma-separated list of origins that can establish connection with Grafana Live.
      # If not set then origin will be matched over root_url. Supports wildcard symbol "*".
      #allowed_origins =

    [dataproxy]
      row_limit = 20000000
---
apiVersion: v1
kind: Service
metadata:
  name: grafana-3rd-test
  namespace: ews-sof-prod
spec:
  type: ClusterIP
  clusterIP: None
  ports:
    - port: 3000
      protocol: TCP
      targetPort: http-grafana
  selector:
    app: grafana-3rd-test
  sessionAffinity: None
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: grafana-3rd-test
  namespace: ews-sof-prod
  annotations:
    cert-manager.io/cluster-issuer: cert-manager-default
spec:
  ingressClassName: nginx
  rules:
    - host: grafana-3rd-test.sof1-prod.kube-experts.com
      http:
        paths:
          - backend:
              service:
                name: grafana-3rd-test
                port:
                  number: 3000
            path: /
            pathType: ImplementationSpecific
  tls:
    - hosts:
        - grafana-3rd-test.sof1-prod.kube-experts.com
      secretName: grafana-3rd-test-tls

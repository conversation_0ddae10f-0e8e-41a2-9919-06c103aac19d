apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-api-ingress-controller-le
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: infrastructure
data:
  nginx-api-ingress-controller-le.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": 793,
      "iteration": 1636558035998,
      "links": [],
      "panels": [
        {
          "datasource": null,
          "description": "ingress=\"boadmin-api-nocors\", path=\"/ws\", exported_service=\"ews-websocket-mgnt\"",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "percentunit"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 3,
            "w": 8,
            "x": 0,
            "y": 0
          },
          "id": 34,
          "options": {
            "colorMode": "value",
            "graphMode": "area",
            "justifyMode": "auto",
            "orientation": "auto",
            "reduceOptions": {
              "calcs": [
                "mean"
              ],
              "fields": "",
              "values": false
            },
            "text": {},
            "textMode": "auto"
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "expr": "sum(rate(nginx_ingress_controller_request_duration_seconds_bucket{ingress=\"$ingress\", path=\"$path\", exported_service=\"$exported_service\", status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_request_duration_seconds_bucket{ingress=\"$ingress\", path=\"$path\", exported_service=\"$exported_service\"}[1m]))",
              "interval": "",
              "legendFormat": "",
              "refId": "A"
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Success rate",
          "type": "stat"
        },
        {
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {
                "align": null,
                "filterable": false
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": []
              },
              "unit": "short"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 16,
            "x": 8,
            "y": 0
          },
          "id": 37,
          "options": {
            "frameIndex": 0,
            "showHeader": true
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "expr": "histogram_quantile(0.50, sum(rate(nginx_ingress_controller_request_duration_seconds_bucket{controller_pod=~\"$controller\",controller_class=~\"$controller_class\",controller_namespace=~\"$namespace\",ingress=~\"$ingress\", path=~\"$path\", exported_service=\"$exported_service\", status!~\"[4-5].*\"}[1m])) by (le, ingress, path, exported_service))",
              "format": "table",
              "instant": true,
              "interval": "",
              "legendFormat": "",
              "refId": "A"
            },
            {
              "expr": "histogram_quantile(0.95, sum(rate(nginx_ingress_controller_request_duration_seconds_bucket{controller_pod=~\"$controller\",controller_class=~\"$controller_class\",controller_namespace=~\"$namespace\",ingress=~\"$ingress\", path=~\"$path\", exported_service=\"$exported_service\"}[1m])) by (le, ingress, path, exported_service))",
              "format": "table",
              "hide": false,
              "instant": true,
              "interval": "",
              "legendFormat": "",
              "refId": "B"
            },
            {
              "expr": "histogram_quantile(0.99, sum(rate(nginx_ingress_controller_request_duration_seconds_bucket{controller_pod=~\"$controller\",controller_class=~\"$controller_class\",controller_namespace=~\"$namespace\",ingress=~\"$ingress\", path=~\"$path\", exported_service=\"$exported_service\"}[1m])) by (le, ingress, path, exported_service))",
              "format": "table",
              "hide": false,
              "instant": true,
              "interval": "",
              "legendFormat": "",
              "refId": "C"
            }
          ],
          "title": "Ingress Percentile Response Times and Transfer Rates",
          "type": "table"
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 5,
            "w": 8,
            "x": 0,
            "y": 3
          },
          "hiddenSeries": false,
          "id": 32,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "sum(rate(nginx_ingress_controller_request_duration_seconds_bucket{ingress=\"$ingress\", path=\"$path\", exported_service=\"$exported_service\", status!~\"[4-5].*\"}[1m]))",
              "interval": "",
              "legendFormat": "",
              "refId": "A"
            },
            {
              "expr": "sum(rate(nginx_ingress_controller_request_duration_seconds_bucket{ingress=\"$ingress\", path=\"$path\", exported_service=\"$exported_service\"}[1m]))",
              "hide": false,
              "interval": "",
              "legendFormat": "",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Requests count - Success / Total",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:195",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:196",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "collapsed": false,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 8
          },
          "id": 4,
          "panels": [],
          "title": "admin-uat-api",
          "type": "row"
        },
        {
          "datasource": null,
          "description": "ingress=\"boadmin-api-nocors\", path=\"/ws\", exported_service=\"ews-websocket-mgnt\"",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "percentunit"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 3,
            "w": 8,
            "x": 0,
            "y": 9
          },
          "id": 2,
          "options": {
            "colorMode": "value",
            "graphMode": "area",
            "justifyMode": "auto",
            "orientation": "auto",
            "reduceOptions": {
              "calcs": [
                "mean"
              ],
              "fields": "",
              "values": false
            },
            "text": {},
            "textMode": "auto"
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "expr": "sum(rate(nginx_ingress_controller_request_duration_seconds_bucket{ingress=\"boadmin-api-nocors\", path=\"/ws\", exported_service=\"ews-websocket-mgnt\", status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_request_duration_seconds_bucket{ingress=\"boadmin-api-nocors\", path=\"/ws\", exported_service=\"ews-websocket-mgnt\"}[1m]))",
              "interval": "",
              "legendFormat": "",
              "refId": "A"
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Success rate",
          "type": "stat"
        },
        {
          "datasource": null,
          "description": "ingress=\"boadmin-api-nocors\", path=\"/ws\", exported_service=\"ews-websocket-mgnt\"",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "percentunit"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 3,
            "w": 8,
            "x": 8,
            "y": 9
          },
          "id": 29,
          "options": {
            "colorMode": "value",
            "graphMode": "area",
            "justifyMode": "auto",
            "orientation": "auto",
            "reduceOptions": {
              "calcs": [
                "mean"
              ],
              "fields": "",
              "values": false
            },
            "text": {},
            "textMode": "auto"
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "expr": "sum(rate(nginx_ingress_controller_request_duration_seconds_bucket{ingress=\"boadmin-api-nocors\", path=\"/api/websocket\", exported_service=\"ews-websocket-mgnt\", status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_request_duration_seconds_bucket{ingress=\"boadmin-api-nocors\", path=\"/api/websocket\", exported_service=\"ews-websocket-mgnt\"}[1m]))",
              "interval": "",
              "legendFormat": "",
              "refId": "A"
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Success rate",
          "type": "stat"
        },
        {
          "datasource": null,
          "description": "ingress=\"boadmin-api-nocors\", path=\"/oauth\", exported_service=\"ews-oauth\"",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {},
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "percentunit"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 3,
            "w": 8,
            "x": 16,
            "y": 9
          },
          "id": 28,
          "options": {
            "colorMode": "value",
            "graphMode": "area",
            "justifyMode": "auto",
            "orientation": "auto",
            "reduceOptions": {
              "calcs": [
                "mean"
              ],
              "fields": "",
              "values": false
            },
            "text": {},
            "textMode": "auto"
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "expr": "sum(rate(nginx_ingress_controller_request_duration_seconds_bucket{ingress=\"boadmin-api-nocors\", path=\"/oauth\", exported_service=\"ews-oauth\", status!~\"[4-5].*\"}[1m])) / sum(rate(nginx_ingress_controller_request_duration_seconds_bucket{ingress=\"boadmin-api-nocors\", path=\"/oauth\", exported_service=\"ews-oauth\"}[1m]))",
              "interval": "",
              "legendFormat": "",
              "refId": "A"
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Success rate",
          "type": "stat"
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 5,
            "w": 8,
            "x": 0,
            "y": 12
          },
          "hiddenSeries": false,
          "id": 31,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "sum(rate(nginx_ingress_controller_request_duration_seconds_bucket{ingress=\"boadmin-api-nocors\", path=\"/ws\", exported_service=\"ews-websocket-mgnt\", status!~\"[4-5].*\"}[1m]))",
              "interval": "",
              "legendFormat": "",
              "refId": "A"
            },
            {
              "expr": "sum(rate(nginx_ingress_controller_request_duration_seconds_bucket{ingress=\"boadmin-api-nocors\", path=\"/ws\", exported_service=\"ews-websocket-mgnt\"}[1m]))",
              "hide": false,
              "interval": "",
              "legendFormat": "",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Requests count - Success / Total",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:195",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:196",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 5,
            "w": 8,
            "x": 8,
            "y": 12
          },
          "hiddenSeries": false,
          "id": 35,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "sum(rate(nginx_ingress_controller_request_duration_seconds_bucket{ingress=\"boadmin-api-nocors\", path=\"/ws\", exported_service=\"ews-websocket-mgnt\", status!~\"[4-5].*\"}[1m]))",
              "interval": "",
              "legendFormat": "",
              "refId": "A"
            },
            {
              "expr": "sum(rate(nginx_ingress_controller_request_duration_seconds_bucket{ingress=\"boadmin-api-nocors\", path=\"/ws\", exported_service=\"ews-websocket-mgnt\"}[1m]))",
              "hide": false,
              "interval": "",
              "legendFormat": "",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Requests count - Success / Total",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:195",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:196",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 5,
            "w": 8,
            "x": 16,
            "y": 12
          },
          "hiddenSeries": false,
          "id": 33,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "sum(rate(nginx_ingress_controller_request_duration_seconds_bucket{ingress=\"boadmin-api-nocors\", path=\"/oauth\", exported_service=\"ews-oauth\", status!~\"[4-5].*\"}[1m]))",
              "interval": "",
              "legendFormat": "",
              "refId": "A"
            },
            {
              "expr": "sum(rate(nginx_ingress_controller_request_duration_seconds_bucket{ingress=\"boadmin-api-nocors\", path=\"/oauth\", exported_service=\"ews-oauth\"}[1m]))",
              "hide": false,
              "interval": "",
              "legendFormat": "",
              "refId": "B"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Requests count - Success / Total",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:195",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:196",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "collapsed": true,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 17
          },
          "id": 6,
          "panels": [],
          "title": "egt-digital-api",
          "type": "row"
        },
        {
          "collapsed": true,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 18
          },
          "id": 8,
          "panels": [],
          "title": "egtdemo-api",
          "type": "row"
        },
        {
          "collapsed": true,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 19
          },
          "id": 10,
          "panels": [],
          "title": "inbet-api",
          "type": "row"
        },
        {
          "collapsed": true,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 20
          },
          "id": 12,
          "panels": [],
          "title": "uat.winbet.bg",
          "type": "row"
        },
        {
          "collapsed": true,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 21
          },
          "id": 14,
          "panels": [],
          "title": "verytest",
          "type": "row"
        },
        {
          "collapsed": true,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 22
          },
          "id": 16,
          "panels": [],
          "title": "wildcard",
          "type": "row"
        },
        {
          "collapsed": true,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 23
          },
          "id": 18,
          "panels": [],
          "title": "winbet-api",
          "type": "row"
        },
        {
          "collapsed": true,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 24
          },
          "id": 20,
          "panels": [],
          "title": "winbet-ro-api",
          "type": "row"
        },
        {
          "collapsed": true,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 25
          },
          "id": 22,
          "panels": [],
          "title": "winbet-rs-api",
          "type": "row"
        },
        {
          "collapsed": true,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 26
          },
          "id": 24,
          "panels": [],
          "title": "winbet-tz-api",
          "type": "row"
        },
        {
          "collapsed": true,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 27
          },
          "id": 26,
          "panels": [],
          "title": "winbet-ua-api",
          "type": "row"
        }
      ],
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": [
          {
            "allValue": ".*",
            "current": {
              "selected": false,
              "text": "infrastructure",
              "value": "infrastructure"
            },
            "datasource": null,
            "definition": "label_values(nginx_ingress_controller_config_hash, controller_namespace)",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": false,
            "label": "Namespace",
            "multi": false,
            "name": "namespace",
            "options": [],
            "query": {
              "query": "label_values(nginx_ingress_controller_config_hash, controller_namespace)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": ".*",
            "current": {
              "selected": false,
              "text": "nginx",
              "value": "nginx"
            },
            "datasource": null,
            "definition": "label_values(nginx_ingress_controller_config_hash{namespace=~\"$namespace\"}, controller_class) ",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": false,
            "label": "Controller Class",
            "multi": false,
            "name": "controller_class",
            "options": [],
            "query": {
              "query": "label_values(nginx_ingress_controller_config_hash{namespace=~\"$namespace\"}, controller_class) ",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": ".*",
            "current": {
              "selected": true,
              "text": "All",
              "value": "$__all"
            },
            "datasource": null,
            "definition": "label_values(nginx_ingress_controller_config_hash{namespace=~\"$namespace\",controller_class=~\"$controller_class\"}, controller_pod) ",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": true,
            "label": "Controller",
            "multi": false,
            "name": "controller",
            "options": [],
            "query": {
              "query": "label_values(nginx_ingress_controller_config_hash{namespace=~\"$namespace\",controller_class=~\"$controller_class\"}, controller_pod) ",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 1,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": ".*",
            "current": {
              "selected": true,
              "text": "boadmin-api-nocors",
              "value": "boadmin-api-nocors"
            },
            "datasource": null,
            "definition": "label_values(nginx_ingress_controller_requests{namespace=~\"$namespace\",controller_class=~\"$controller_class\",controller=~\"$controller\"}, ingress) ",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": false,
            "label": "Ingress",
            "multi": false,
            "name": "ingress",
            "options": [],
            "query": {
              "query": "label_values(nginx_ingress_controller_requests{namespace=~\"$namespace\",controller_class=~\"$controller_class\",controller=~\"$controller\"}, ingress) ",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 1,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {
              "selected": true,
              "text": "/ws",
              "value": "/ws"
            },
            "datasource": null,
            "definition": "label_values(nginx_ingress_controller_request_duration_seconds_bucket{namespace=~\"$namespace\",controller_class=~\"$controller_class\",controller=~\"$controller\", ingress=~\"$ingress\"}, path)",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": false,
            "label": "Path",
            "multi": false,
            "name": "path",
            "options": [],
            "query": {
              "query": "label_values(nginx_ingress_controller_request_duration_seconds_bucket{namespace=~\"$namespace\",controller_class=~\"$controller_class\",controller=~\"$controller\", ingress=~\"$ingress\"}, path)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 1,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {
              "selected": false,
              "text": "ews-websocket-mgnt",
              "value": "ews-websocket-mgnt"
            },
            "datasource": null,
            "definition": "label_values(nginx_ingress_controller_request_duration_seconds_bucket{namespace=~\"$namespace\",controller_class=~\"$controller_class\",controller=~\"$controller\", ingress=~\"$ingress\", path=~\"$path\"}, exported_service)",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": false,
            "label": "Exported Service",
            "multi": false,
            "name": "exported_service",
            "options": [],
            "query": {
              "query": "label_values(nginx_ingress_controller_request_duration_seconds_bucket{namespace=~\"$namespace\",controller_class=~\"$controller_class\",controller=~\"$controller\", ingress=~\"$ingress\", path=~\"$path\"}, exported_service)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 1,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          }
        ]
      },
      "time": {
        "from": "now-1h",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "NGINX Api Ingress controller Latency Endpoints",
      "uid": "JBuLTLKnk",
      "version": 4
    }
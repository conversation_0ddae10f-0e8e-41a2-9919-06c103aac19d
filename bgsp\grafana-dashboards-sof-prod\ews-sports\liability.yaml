apiVersion: v1
kind: ConfigMap
metadata:
  name: liability
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: ews-sports
data:
  liability.json: |-
    {
    	"annotations": {
    		"list": [
    			{
    				"builtIn": 1,
    				"datasource": "-- Grafana --",
    				"enable": true,
    				"hide": true,
    				"iconColor": "rgba(0, 211, 255, 1)",
    				"name": "Annotations & Alerts",
    				"type": "dashboard"
    			}
    		]
    	},
    	"editable": true,
    	"gnetId": null,
    	"graphTooltip": 0,
    	"iteration": 1728484347238,
    	"links": [],
    	"panels": [
    		{
    			"aliasColors": {},
    			"bars": false,
    			"dashLength": 10,
    			"dashes": false,
    			"datasource": null,
    			"fieldConfig": {
    				"defaults": {
    					"custom": {}
    				},
    				"overrides": []
    			},
    			"fill": 1,
    			"fillGradient": 0,
    			"gridPos": {
    				"h": 8,
    				"w": 12,
    				"x": 0,
    				"y": 0
    			},
    			"hiddenSeries": false,
    			"id": 62,
    			"legend": {
    				"alignAsTable": true,
    				"avg": false,
    				"current": true,
    				"max": true,
    				"min": false,
    				"rightSide": true,
    				"show": true,
    				"total": false,
    				"values": true
    			},
    			"lines": true,
    			"linewidth": 1,
    			"nullPointMode": "null",
    			"options": {
    				"alertThreshold": true
    			},
    			"percentage": false,
    			"pluginVersion": "7.4.2",
    			"pointradius": 2,
    			"points": false,
    			"renderer": "flot",
    			"seriesOverrides": [],
    			"spaceLength": 10,
    			"stack": false,
    			"steppedLine": false,
    			"targets": [
    				{
    					"expr": "kafka_consumergroup_lag_sum{consumergroup=~\"ExposureConsumer\", topic=~\"sport.liability.exposure\", namespace=~\"$namespace\"}",
    					"interval": "",
    					"legendFormat": "{{consumergroup}}",
    					"refId": "A"
    				},
    				{
    					"expr": "kafka_consumergroup_lag_sum{consumergroup=~\"RiskConsumer\", topic=~\"sport.liability.risk\", namespace=~\"$namespace\"}",
    					"hide": false,
    					"interval": "",
    					"legendFormat": "{{consumergroup}}",
    					"refId": "B"
    				}
    			],
    			"thresholds": [],
    			"timeFrom": null,
    			"timeRegions": [],
    			"timeShift": null,
    			"title": "Kafka lag",
    			"tooltip": {
    				"shared": true,
    				"sort": 0,
    				"value_type": "individual"
    			},
    			"type": "graph",
    			"xaxis": {
    				"buckets": null,
    				"mode": "time",
    				"name": null,
    				"show": true,
    				"values": []
    			},
    			"yaxes": [
    				{
    					"$hashKey": "object:530",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				},
    				{
    					"$hashKey": "object:531",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				}
    			],
    			"yaxis": {
    				"align": false,
    				"alignLevel": null
    			}
    		},
    		{
    			"aliasColors": {},
    			"bars": false,
    			"dashLength": 10,
    			"dashes": false,
    			"datasource": null,
    			"fieldConfig": {
    				"defaults": {
    					"custom": {}
    				},
    				"overrides": []
    			},
    			"fill": 1,
    			"fillGradient": 0,
    			"gridPos": {
    				"h": 8,
    				"w": 6,
    				"x": 12,
    				"y": 0
    			},
    			"hiddenSeries": false,
    			"id": 48,
    			"legend": {
    				"avg": false,
    				"current": false,
    				"max": false,
    				"min": false,
    				"show": true,
    				"total": false,
    				"values": false
    			},
    			"lines": true,
    			"linewidth": 1,
    			"nullPointMode": "null",
    			"options": {
    				"alertThreshold": true
    			},
    			"percentage": false,
    			"pluginVersion": "7.4.2",
    			"pointradius": 2,
    			"points": false,
    			"renderer": "flot",
    			"seriesOverrides": [],
    			"spaceLength": 10,
    			"stack": false,
    			"steppedLine": false,
    			"targets": [
    				{
    					"expr": "process_num_threads{job=\"$job\",namespace=\"$namespace\"}",
    					"interval": "",
    					"legendFormat": "{{pod}}",
    					"refId": "A"
    				}
    			],
    			"thresholds": [],
    			"timeFrom": null,
    			"timeRegions": [],
    			"timeShift": null,
    			"title": "Process Threads",
    			"tooltip": {
    				"shared": true,
    				"sort": 0,
    				"value_type": "individual"
    			},
    			"type": "graph",
    			"xaxis": {
    				"buckets": null,
    				"mode": "time",
    				"name": null,
    				"show": true,
    				"values": []
    			},
    			"yaxes": [
    				{
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				},
    				{
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				}
    			],
    			"yaxis": {
    				"align": false,
    				"alignLevel": null
    			}
    		},
    		{
    			"aliasColors": {},
    			"bars": false,
    			"dashLength": 10,
    			"dashes": false,
    			"datasource": null,
    			"fieldConfig": {
    				"defaults": {
    					"custom": {}
    				},
    				"overrides": []
    			},
    			"fill": 1,
    			"fillGradient": 0,
    			"gridPos": {
    				"h": 8,
    				"w": 6,
    				"x": 18,
    				"y": 0
    			},
    			"hiddenSeries": false,
    			"id": 52,
    			"legend": {
    				"avg": false,
    				"current": false,
    				"max": false,
    				"min": false,
    				"show": true,
    				"total": false,
    				"values": false
    			},
    			"lines": true,
    			"linewidth": 1,
    			"nullPointMode": "null",
    			"options": {
    				"alertThreshold": true
    			},
    			"percentage": false,
    			"pluginVersion": "7.4.2",
    			"pointradius": 2,
    			"points": false,
    			"renderer": "flot",
    			"seriesOverrides": [],
    			"spaceLength": 10,
    			"stack": false,
    			"steppedLine": false,
    			"targets": [
    				{
    					"expr": "rate(process_cpu_seconds_total{job=\"$job\", namespace=\"$namespace\"}[5m])",
    					"interval": "",
    					"legendFormat": "",
    					"refId": "A"
    				}
    			],
    			"thresholds": [],
    			"timeFrom": null,
    			"timeRegions": [],
    			"timeShift": null,
    			"title": "Process CPU Seconds",
    			"tooltip": {
    				"shared": true,
    				"sort": 0,
    				"value_type": "individual"
    			},
    			"type": "graph",
    			"xaxis": {
    				"buckets": null,
    				"mode": "time",
    				"name": null,
    				"show": true,
    				"values": []
    			},
    			"yaxes": [
    				{
    					"$hashKey": "object:654",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				},
    				{
    					"$hashKey": "object:655",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				}
    			],
    			"yaxis": {
    				"align": false,
    				"alignLevel": null
    			}
    		},
    		{
    			"aliasColors": {},
    			"bars": false,
    			"dashLength": 10,
    			"dashes": false,
    			"datasource": null,
    			"fieldConfig": {
    				"defaults": {
    					"custom": {}
    				},
    				"overrides": []
    			},
    			"fill": 1,
    			"fillGradient": 0,
    			"gridPos": {
    				"h": 8,
    				"w": 12,
    				"x": 0,
    				"y": 8
    			},
    			"hiddenSeries": false,
    			"id": 60,
    			"legend": {
    				"avg": false,
    				"current": false,
    				"max": false,
    				"min": false,
    				"show": true,
    				"total": false,
    				"values": false
    			},
    			"lines": true,
    			"linewidth": 1,
    			"nullPointMode": "null",
    			"options": {
    				"alertThreshold": true
    			},
    			"percentage": false,
    			"pluginVersion": "7.4.2",
    			"pointradius": 2,
    			"points": false,
    			"renderer": "flot",
    			"seriesOverrides": [],
    			"spaceLength": 10,
    			"stack": false,
    			"steppedLine": false,
    			"targets": [
    				{
    					"expr": "sum((1000 * rate(riskworker_batch_processing_time_sum{job=~\"$job\", namespace=~\"$namespace\"}[10m]))/ \nrate(riskworker_batch_processing_time_count{job=~\"$job\", namespace=~\"$namespace\"}[10m]))",
    					"interval": "",
    					"legendFormat": "Risk",
    					"refId": "A"
    				},
    				{
    					"expr": "sum((1000 * rate(exposureworker_batch_processing_time_sum{job=~\"$job\", namespace=~\"$namespace\"}[10m]))/ \nrate(exposureworker_batch_processing_time_count{job=~\"$job\", namespace=~\"$namespace\"}[10m]))",
    					"hide": false,
    					"interval": "",
    					"legendFormat": "Exposure",
    					"refId": "B"
    				}
    			],
    			"thresholds": [],
    			"timeFrom": null,
    			"timeRegions": [],
    			"timeShift": null,
    			"title": "Batch Processing Time",
    			"tooltip": {
    				"shared": true,
    				"sort": 0,
    				"value_type": "individual"
    			},
    			"type": "graph",
    			"xaxis": {
    				"buckets": null,
    				"mode": "time",
    				"name": null,
    				"show": true,
    				"values": []
    			},
    			"yaxes": [
    				{
    					"$hashKey": "object:273",
    					"decimals": null,
    					"format": "µs",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				},
    				{
    					"$hashKey": "object:274",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				}
    			],
    			"yaxis": {
    				"align": false,
    				"alignLevel": null
    			}
    		},
    		{
    			"aliasColors": {},
    			"bars": false,
    			"dashLength": 10,
    			"dashes": false,
    			"datasource": null,
    			"fieldConfig": {
    				"defaults": {
    					"custom": {}
    				},
    				"overrides": []
    			},
    			"fill": 1,
    			"fillGradient": 0,
    			"gridPos": {
    				"h": 8,
    				"w": 6,
    				"x": 12,
    				"y": 8
    			},
    			"hiddenSeries": false,
    			"id": 54,
    			"legend": {
    				"avg": false,
    				"current": false,
    				"max": false,
    				"min": false,
    				"show": true,
    				"total": false,
    				"values": false
    			},
    			"lines": true,
    			"linewidth": 1,
    			"nullPointMode": "null",
    			"options": {
    				"alertThreshold": true
    			},
    			"percentage": false,
    			"pluginVersion": "7.4.2",
    			"pointradius": 2,
    			"points": false,
    			"renderer": "flot",
    			"seriesOverrides": [],
    			"spaceLength": 10,
    			"stack": false,
    			"steppedLine": false,
    			"targets": [
    				{
    					"expr": "sum(http_requests_in_progress{job=\"$job\", namespace=\"$namespace\"})",
    					"interval": "",
    					"legendFormat": "",
    					"refId": "A"
    				}
    			],
    			"thresholds": [],
    			"timeFrom": null,
    			"timeRegions": [],
    			"timeShift": null,
    			"title": "Requests In Progress",
    			"tooltip": {
    				"shared": true,
    				"sort": 0,
    				"value_type": "individual"
    			},
    			"type": "graph",
    			"xaxis": {
    				"buckets": null,
    				"mode": "time",
    				"name": null,
    				"show": true,
    				"values": []
    			},
    			"yaxes": [
    				{
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				},
    				{
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				}
    			],
    			"yaxis": {
    				"align": false,
    				"alignLevel": null
    			}
    		},
    		{
    			"aliasColors": {},
    			"bars": false,
    			"dashLength": 10,
    			"dashes": false,
    			"datasource": null,
    			"fieldConfig": {
    				"defaults": {
    					"custom": {}
    				},
    				"overrides": []
    			},
    			"fill": 1,
    			"fillGradient": 0,
    			"gridPos": {
    				"h": 8,
    				"w": 6,
    				"x": 18,
    				"y": 8
    			},
    			"hiddenSeries": false,
    			"id": 56,
    			"legend": {
    				"avg": false,
    				"current": false,
    				"max": false,
    				"min": false,
    				"show": true,
    				"total": false,
    				"values": false
    			},
    			"lines": true,
    			"linewidth": 1,
    			"nullPointMode": "null",
    			"options": {
    				"alertThreshold": true
    			},
    			"percentage": false,
    			"pluginVersion": "7.4.2",
    			"pointradius": 2,
    			"points": false,
    			"renderer": "flot",
    			"seriesOverrides": [],
    			"spaceLength": 10,
    			"stack": false,
    			"steppedLine": false,
    			"targets": [
    				{
    					"expr": "rate(http_requests_received_total{job=\"$job\", namespace=\"$namespace\"}[30m])",
    					"interval": "",
    					"legendFormat": "{{ action }} {{ code }}",
    					"refId": "A"
    				}
    			],
    			"thresholds": [],
    			"timeFrom": null,
    			"timeRegions": [],
    			"timeShift": null,
    			"title": "Requests Received",
    			"tooltip": {
    				"shared": true,
    				"sort": 0,
    				"value_type": "individual"
    			},
    			"type": "graph",
    			"xaxis": {
    				"buckets": null,
    				"mode": "time",
    				"name": null,
    				"show": true,
    				"values": []
    			},
    			"yaxes": [
    				{
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				},
    				{
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				}
    			],
    			"yaxis": {
    				"align": false,
    				"alignLevel": null
    			}
    		},
    		{
    			"collapsed": false,
    			"datasource": null,
    			"gridPos": {
    				"h": 1,
    				"w": 24,
    				"x": 0,
    				"y": 16
    			},
    			"id": 37,
    			"panels": [],
    			"title": "General",
    			"type": "row"
    		},
    		{
    			"aliasColors": {},
    			"bars": false,
    			"dashLength": 10,
    			"dashes": false,
    			"datasource": null,
    			"fieldConfig": {
    				"defaults": {
    					"custom": {}
    				},
    				"overrides": []
    			},
    			"fill": 1,
    			"fillGradient": 0,
    			"gridPos": {
    				"h": 8,
    				"w": 11,
    				"x": 0,
    				"y": 17
    			},
    			"hiddenSeries": false,
    			"id": 50,
    			"legend": {
    				"avg": false,
    				"current": false,
    				"max": false,
    				"min": false,
    				"show": true,
    				"total": false,
    				"values": false
    			},
    			"lines": true,
    			"linewidth": 1,
    			"nullPointMode": "null",
    			"options": {
    				"alertThreshold": true
    			},
    			"percentage": false,
    			"pluginVersion": "7.4.2",
    			"pointradius": 2,
    			"points": false,
    			"renderer": "flot",
    			"seriesOverrides": [],
    			"spaceLength": 10,
    			"stack": false,
    			"steppedLine": false,
    			"targets": [
    				{
    					"expr": "cache_items_count{namespace=\"$namespace\", job=\"$job\"}",
    					"interval": "",
    					"legendFormat": "{{ cache }}",
    					"refId": "A"
    				}
    			],
    			"thresholds": [],
    			"timeFrom": null,
    			"timeRegions": [],
    			"timeShift": null,
    			"title": "Cache Items Count",
    			"tooltip": {
    				"shared": true,
    				"sort": 0,
    				"value_type": "individual"
    			},
    			"type": "graph",
    			"xaxis": {
    				"buckets": null,
    				"mode": "time",
    				"name": null,
    				"show": true,
    				"values": []
    			},
    			"yaxes": [
    				{
    					"$hashKey": "object:839",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				},
    				{
    					"$hashKey": "object:840",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				}
    			],
    			"yaxis": {
    				"align": false,
    				"alignLevel": null
    			}
    		},
    		{
    			"aliasColors": {},
    			"bars": false,
    			"dashLength": 10,
    			"dashes": false,
    			"datasource": null,
    			"description": "",
    			"fieldConfig": {
    				"defaults": {
    					"custom": {}
    				},
    				"overrides": []
    			},
    			"fill": 1,
    			"fillGradient": 0,
    			"gridPos": {
    				"h": 8,
    				"w": 6,
    				"x": 12,
    				"y": 17
    			},
    			"hiddenSeries": false,
    			"id": 14,
    			"legend": {
    				"alignAsTable": true,
    				"avg": false,
    				"current": true,
    				"max": false,
    				"min": false,
    				"rightSide": false,
    				"show": true,
    				"total": true,
    				"values": true
    			},
    			"lines": true,
    			"linewidth": 1,
    			"nullPointMode": "null",
    			"options": {
    				"alertThreshold": true
    			},
    			"percentage": false,
    			"pluginVersion": "7.4.2",
    			"pointradius": 2,
    			"points": false,
    			"renderer": "flot",
    			"seriesOverrides": [],
    			"spaceLength": 10,
    			"stack": false,
    			"steppedLine": false,
    			"targets": [
    				{
    					"expr": "rate(execution_failed_total{instance=\"$instance\", uri!~\".*actuator.*\"}[1m])",
    					"interval": "",
    					"legendFormat": "{{action}} [{{code}}] {{controller}}",
    					"refId": "A"
    				},
    				{
    					"expr": "sum(rate(message_publish_errors{namespace=\"$namespace\"}[10m])) by (message_type,period)",
    					"hide": false,
    					"interval": "",
    					"legendFormat": "RabbitMQ {{message_type}} {{exception}}",
    					"refId": "B"
    				}
    			],
    			"thresholds": [],
    			"timeFrom": null,
    			"timeRegions": [],
    			"timeShift": null,
    			"title": "Errors",
    			"tooltip": {
    				"shared": true,
    				"sort": 0,
    				"value_type": "individual"
    			},
    			"type": "graph",
    			"xaxis": {
    				"buckets": null,
    				"mode": "time",
    				"name": null,
    				"show": true,
    				"values": []
    			},
    			"yaxes": [
    				{
    					"$hashKey": "object:755",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				},
    				{
    					"$hashKey": "object:756",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				}
    			],
    			"yaxis": {
    				"align": false,
    				"alignLevel": null
    			}
    		},
    		{
    			"aliasColors": {},
    			"bars": false,
    			"dashLength": 10,
    			"dashes": false,
    			"datasource": null,
    			"description": "",
    			"fieldConfig": {
    				"defaults": {
    					"custom": {}
    				},
    				"overrides": []
    			},
    			"fill": 1,
    			"fillGradient": 0,
    			"gridPos": {
    				"h": 8,
    				"w": 6,
    				"x": 18,
    				"y": 17
    			},
    			"hiddenSeries": false,
    			"id": 38,
    			"legend": {
    				"alignAsTable": true,
    				"avg": false,
    				"current": true,
    				"max": false,
    				"min": false,
    				"rightSide": false,
    				"show": true,
    				"total": true,
    				"values": true
    			},
    			"lines": true,
    			"linewidth": 1,
    			"nullPointMode": "null",
    			"options": {
    				"alertThreshold": true
    			},
    			"percentage": false,
    			"pluginVersion": "7.4.2",
    			"pointradius": 2,
    			"points": false,
    			"renderer": "flot",
    			"seriesOverrides": [],
    			"spaceLength": 10,
    			"stack": false,
    			"steppedLine": false,
    			"targets": [
    				{
    					"expr": "sum(processing_queue_lag{instance=\"$instance\", namespace=\"$namespace\"}) by (queue)",
    					"interval": "",
    					"legendFormat": "{{queue}} | $job ",
    					"refId": "A"
    				}
    			],
    			"thresholds": [],
    			"timeFrom": null,
    			"timeRegions": [],
    			"timeShift": null,
    			"title": "Processing Lags",
    			"tooltip": {
    				"shared": true,
    				"sort": 0,
    				"value_type": "individual"
    			},
    			"type": "graph",
    			"xaxis": {
    				"buckets": null,
    				"mode": "time",
    				"name": null,
    				"show": true,
    				"values": []
    			},
    			"yaxes": [
    				{
    					"$hashKey": "object:755",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				},
    				{
    					"$hashKey": "object:756",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				}
    			],
    			"yaxis": {
    				"align": false,
    				"alignLevel": null
    			}
    		},
    		{
    			"aliasColors": {},
    			"bars": false,
    			"dashLength": 10,
    			"dashes": false,
    			"datasource": null,
    			"fieldConfig": {
    				"defaults": {
    					"custom": {}
    				},
    				"overrides": []
    			},
    			"fill": 1,
    			"fillGradient": 0,
    			"gridPos": {
    				"h": 8,
    				"w": 6,
    				"x": 0,
    				"y": 25
    			},
    			"hiddenSeries": false,
    			"id": 2,
    			"legend": {
    				"alignAsTable": true,
    				"avg": true,
    				"current": true,
    				"max": true,
    				"min": true,
    				"show": true,
    				"total": false,
    				"values": true
    			},
    			"lines": true,
    			"linewidth": 1,
    			"nullPointMode": "null",
    			"options": {
    				"alertThreshold": true
    			},
    			"percentage": false,
    			"pluginVersion": "7.4.2",
    			"pointradius": 2,
    			"points": false,
    			"renderer": "flot",
    			"seriesOverrides": [],
    			"spaceLength": 10,
    			"stack": false,
    			"steppedLine": false,
    			"targets": [
    				{
    					"expr": "sum(rate(container_cpu_usage_seconds_total{pod=\"$podname\"}[5m]))",
    					"interval": "",
    					"legendFormat": "cpu usage",
    					"refId": "A"
    				}
    			],
    			"thresholds": [],
    			"timeFrom": null,
    			"timeRegions": [],
    			"timeShift": null,
    			"title": "Number of cores used",
    			"tooltip": {
    				"shared": true,
    				"sort": 0,
    				"value_type": "individual"
    			},
    			"type": "graph",
    			"xaxis": {
    				"buckets": null,
    				"mode": "time",
    				"name": null,
    				"show": true,
    				"values": []
    			},
    			"yaxes": [
    				{
    					"$hashKey": "object:91",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				},
    				{
    					"$hashKey": "object:92",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				}
    			],
    			"yaxis": {
    				"align": false,
    				"alignLevel": null
    			}
    		},
    		{
    			"aliasColors": {},
    			"bars": false,
    			"dashLength": 10,
    			"dashes": false,
    			"datasource": null,
    			"fieldConfig": {
    				"defaults": {
    					"custom": {}
    				},
    				"overrides": []
    			},
    			"fill": 1,
    			"fillGradient": 0,
    			"gridPos": {
    				"h": 8,
    				"w": 6,
    				"x": 6,
    				"y": 25
    			},
    			"hiddenSeries": false,
    			"id": 24,
    			"legend": {
    				"avg": false,
    				"current": false,
    				"max": false,
    				"min": false,
    				"show": true,
    				"total": false,
    				"values": false
    			},
    			"lines": true,
    			"linewidth": 1,
    			"nullPointMode": "null",
    			"options": {
    				"alertThreshold": true
    			},
    			"percentage": false,
    			"pluginVersion": "7.4.2",
    			"pointradius": 2,
    			"points": false,
    			"renderer": "flot",
    			"seriesOverrides": [],
    			"spaceLength": 10,
    			"stack": false,
    			"steppedLine": false,
    			"targets": [
    				{
    					"expr": "process_private_memory_bytes{instance=\"$instance\"}",
    					"interval": "",
    					"legendFormat": "",
    					"refId": "A"
    				}
    			],
    			"thresholds": [],
    			"timeFrom": null,
    			"timeRegions": [],
    			"timeShift": null,
    			"title": "process_private_memory_bytes",
    			"tooltip": {
    				"shared": true,
    				"sort": 0,
    				"value_type": "individual"
    			},
    			"type": "graph",
    			"xaxis": {
    				"buckets": null,
    				"mode": "time",
    				"name": null,
    				"show": true,
    				"values": []
    			},
    			"yaxes": [
    				{
    					"$hashKey": "object:585",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				},
    				{
    					"$hashKey": "object:586",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				}
    			],
    			"yaxis": {
    				"align": false,
    				"alignLevel": null
    			}
    		},
    		{
    			"collapsed": false,
    			"datasource": null,
    			"gridPos": {
    				"h": 1,
    				"w": 24,
    				"x": 0,
    				"y": 33
    			},
    			"id": 22,
    			"panels": [],
    			"title": "Performance",
    			"type": "row"
    		},
    		{
    			"aliasColors": {},
    			"bars": false,
    			"dashLength": 10,
    			"dashes": false,
    			"datasource": null,
    			"fieldConfig": {
    				"defaults": {
    					"custom": {}
    				},
    				"overrides": []
    			},
    			"fill": 1,
    			"fillGradient": 0,
    			"gridPos": {
    				"h": 9,
    				"w": 12,
    				"x": 0,
    				"y": 34
    			},
    			"hiddenSeries": false,
    			"id": 34,
    			"legend": {
    				"alignAsTable": true,
    				"avg": false,
    				"current": true,
    				"max": false,
    				"min": false,
    				"rightSide": true,
    				"show": true,
    				"total": true,
    				"values": true
    			},
    			"lines": true,
    			"linewidth": 1,
    			"nullPointMode": "null",
    			"options": {
    				"alertThreshold": true
    			},
    			"percentage": false,
    			"pluginVersion": "7.4.2",
    			"pointradius": 2,
    			"points": false,
    			"renderer": "flot",
    			"seriesOverrides": [],
    			"spaceLength": 10,
    			"stack": false,
    			"steppedLine": false,
    			"targets": [
    				{
    					"expr": "rate(execution_duration_seconds_sum{instance=\"$instance\", uri!~\".*actuator.*\"}[5m])/rate(execution_duration_seconds_count{instance=\"$instance\", uri!~\".*actuator.*\"}[5m])",
    					"interval": "",
    					"legendFormat": "{{action}} [{{code}}] {{controller}}",
    					"refId": "A"
    				}
    			],
    			"thresholds": [],
    			"timeFrom": null,
    			"timeRegions": [],
    			"timeShift": null,
    			"title": "Execution Durations in Seconds",
    			"tooltip": {
    				"shared": true,
    				"sort": 0,
    				"value_type": "individual"
    			},
    			"type": "graph",
    			"xaxis": {
    				"buckets": null,
    				"mode": "time",
    				"name": null,
    				"show": true,
    				"values": []
    			},
    			"yaxes": [
    				{
    					"$hashKey": "object:755",
    					"format": "s",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				},
    				{
    					"$hashKey": "object:756",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				}
    			],
    			"yaxis": {
    				"align": false,
    				"alignLevel": null
    			}
    		},
    		{
    			"aliasColors": {},
    			"bars": false,
    			"dashLength": 10,
    			"dashes": false,
    			"datasource": null,
    			"fieldConfig": {
    				"defaults": {
    					"custom": {}
    				},
    				"overrides": []
    			},
    			"fill": 1,
    			"fillGradient": 0,
    			"gridPos": {
    				"h": 9,
    				"w": 12,
    				"x": 12,
    				"y": 34
    			},
    			"hiddenSeries": false,
    			"id": 41,
    			"legend": {
    				"alignAsTable": true,
    				"avg": false,
    				"current": true,
    				"max": false,
    				"min": false,
    				"rightSide": true,
    				"show": true,
    				"total": true,
    				"values": true
    			},
    			"lines": true,
    			"linewidth": 1,
    			"nullPointMode": "null",
    			"options": {
    				"alertThreshold": true
    			},
    			"percentage": false,
    			"pluginVersion": "7.4.2",
    			"pointradius": 2,
    			"points": false,
    			"renderer": "flot",
    			"seriesOverrides": [],
    			"spaceLength": 10,
    			"stack": false,
    			"steppedLine": false,
    			"targets": [
    				{
    					"expr": "sum(rate(message_size_bytes_sum{namespace=\"$namespace\"}[10m])) by (message_type) / sum(rate(message_size_bytes_count{namespace=\"$namespace\"}[10m])) by (message_type)",
    					"interval": "",
    					"legendFormat": "{{message_type}}",
    					"refId": "A"
    				}
    			],
    			"thresholds": [],
    			"timeFrom": null,
    			"timeRegions": [],
    			"timeShift": null,
    			"title": "RabbitMQ - Bytes (per second)",
    			"tooltip": {
    				"shared": true,
    				"sort": 0,
    				"value_type": "individual"
    			},
    			"type": "graph",
    			"xaxis": {
    				"buckets": null,
    				"mode": "time",
    				"name": null,
    				"show": true,
    				"values": []
    			},
    			"yaxes": [
    				{
    					"$hashKey": "object:755",
    					"format": "deckbytes",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				},
    				{
    					"$hashKey": "object:756",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				}
    			],
    			"yaxis": {
    				"align": false,
    				"alignLevel": null
    			}
    		},
    		{
    			"aliasColors": {},
    			"bars": false,
    			"dashLength": 10,
    			"dashes": false,
    			"datasource": null,
    			"description": "",
    			"fieldConfig": {
    				"defaults": {
    					"custom": {}
    				},
    				"overrides": []
    			},
    			"fill": 1,
    			"fillGradient": 0,
    			"gridPos": {
    				"h": 10,
    				"w": 12,
    				"x": 0,
    				"y": 43
    			},
    			"hiddenSeries": false,
    			"id": 35,
    			"legend": {
    				"alignAsTable": true,
    				"avg": false,
    				"current": true,
    				"max": false,
    				"min": false,
    				"rightSide": true,
    				"show": true,
    				"total": true,
    				"values": true
    			},
    			"lines": true,
    			"linewidth": 1,
    			"nullPointMode": "null",
    			"options": {
    				"alertThreshold": true
    			},
    			"percentage": false,
    			"pluginVersion": "7.4.2",
    			"pointradius": 2,
    			"points": false,
    			"renderer": "flot",
    			"seriesOverrides": [],
    			"spaceLength": 10,
    			"stack": false,
    			"steppedLine": false,
    			"targets": [
    				{
    					"expr": "sum(rate(execution_duration_seconds_count{job=\"$job\",namespace=\"$namespace\"}[$__rate_interval])) by (controller,action,job)",
    					"interval": "",
    					"legendFormat": "{{job}}.{{controller}}.{{action}}",
    					"refId": "A"
    				},
    				{
    					"expr": "sum(rate(execution_duration_seconds_count{job=\"$job\",namespace=\"$namespace\"}[$__rate_interval])) by (controller,action,job)",
    					"hide": false,
    					"interval": "",
    					"legendFormat": "{{action}} [{{code}}] {{controller}}",
    					"refId": "B"
    				}
    			],
    			"thresholds": [],
    			"timeFrom": null,
    			"timeRegions": [],
    			"timeShift": null,
    			"title": "Execution Rate",
    			"tooltip": {
    				"shared": true,
    				"sort": 0,
    				"value_type": "individual"
    			},
    			"type": "graph",
    			"xaxis": {
    				"buckets": null,
    				"mode": "time",
    				"name": null,
    				"show": true,
    				"values": []
    			},
    			"yaxes": [
    				{
    					"$hashKey": "object:755",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				},
    				{
    					"$hashKey": "object:756",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				}
    			],
    			"yaxis": {
    				"align": false,
    				"alignLevel": null
    			}
    		},
    		{
    			"aliasColors": {},
    			"bars": false,
    			"dashLength": 10,
    			"dashes": false,
    			"datasource": null,
    			"description": "",
    			"fieldConfig": {
    				"defaults": {
    					"custom": {}
    				},
    				"overrides": []
    			},
    			"fill": 1,
    			"fillGradient": 0,
    			"gridPos": {
    				"h": 10,
    				"w": 12,
    				"x": 12,
    				"y": 43
    			},
    			"hiddenSeries": false,
    			"id": 42,
    			"legend": {
    				"alignAsTable": true,
    				"avg": false,
    				"current": true,
    				"max": false,
    				"min": false,
    				"rightSide": true,
    				"show": true,
    				"total": true,
    				"values": true
    			},
    			"lines": true,
    			"linewidth": 1,
    			"nullPointMode": "null",
    			"options": {
    				"alertThreshold": true
    			},
    			"percentage": false,
    			"pluginVersion": "7.4.2",
    			"pointradius": 2,
    			"points": false,
    			"renderer": "flot",
    			"seriesOverrides": [],
    			"spaceLength": 10,
    			"stack": false,
    			"steppedLine": false,
    			"targets": [
    				{
    					"expr": "sum(rate(message_size_bytes_count{namespace=\"$namespace\"}[10m])) by (message_type)",
    					"interval": "",
    					"legendFormat": "{{message_type}}",
    					"refId": "A"
    				},
    				{
    					"expr": "sum(rate(duplicated_messages{namespace=\"$namespace\"}[10m])) by (message_type)",
    					"hide": false,
    					"interval": "",
    					"legendFormat": "{{message_type}} (Deduplicated)",
    					"refId": "B"
    				}
    			],
    			"thresholds": [],
    			"timeFrom": null,
    			"timeRegions": [],
    			"timeShift": null,
    			"title": "RabbitMQ - Messages (per second)",
    			"tooltip": {
    				"shared": true,
    				"sort": 0,
    				"value_type": "individual"
    			},
    			"type": "graph",
    			"xaxis": {
    				"buckets": null,
    				"mode": "time",
    				"name": null,
    				"show": true,
    				"values": []
    			},
    			"yaxes": [
    				{
    					"$hashKey": "object:755",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				},
    				{
    					"$hashKey": "object:756",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				}
    			],
    			"yaxis": {
    				"align": false,
    				"alignLevel": null
    			}
    		},
    		{
    			"aliasColors": {},
    			"bars": false,
    			"dashLength": 10,
    			"dashes": false,
    			"datasource": null,
    			"description": "",
    			"fieldConfig": {
    				"defaults": {
    					"custom": {}
    				},
    				"overrides": []
    			},
    			"fill": 1,
    			"fillGradient": 0,
    			"gridPos": {
    				"h": 10,
    				"w": 12,
    				"x": 12,
    				"y": 53
    			},
    			"hiddenSeries": false,
    			"id": 44,
    			"legend": {
    				"alignAsTable": true,
    				"avg": false,
    				"current": true,
    				"max": false,
    				"min": false,
    				"rightSide": true,
    				"show": true,
    				"total": true,
    				"values": true
    			},
    			"lines": true,
    			"linewidth": 1,
    			"nullPointMode": "null",
    			"options": {
    				"alertThreshold": true
    			},
    			"percentage": false,
    			"pluginVersion": "7.4.2",
    			"pointradius": 2,
    			"points": false,
    			"renderer": "flot",
    			"seriesOverrides": [],
    			"spaceLength": 10,
    			"stack": false,
    			"steppedLine": false,
    			"targets": [
    				{
    					"expr": "avg(cache_items_count{job=\"ews-liability-api\"}) by (cache)",
    					"interval": "",
    					"legendFormat": "{{cache}}",
    					"refId": "A"
    				}
    			],
    			"thresholds": [],
    			"timeFrom": null,
    			"timeRegions": [],
    			"timeShift": null,
    			"title": "Cache Sizes",
    			"tooltip": {
    				"shared": true,
    				"sort": 0,
    				"value_type": "individual"
    			},
    			"type": "graph",
    			"xaxis": {
    				"buckets": null,
    				"mode": "time",
    				"name": null,
    				"show": true,
    				"values": []
    			},
    			"yaxes": [
    				{
    					"$hashKey": "object:755",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				},
    				{
    					"$hashKey": "object:756",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				}
    			],
    			"yaxis": {
    				"align": false,
    				"alignLevel": null
    			}
    		},
    		{
    			"collapsed": false,
    			"datasource": null,
    			"gridPos": {
    				"h": 1,
    				"w": 24,
    				"x": 0,
    				"y": 63
    			},
    			"id": 40,
    			"panels": [],
    			"title": "Resources",
    			"type": "row"
    		},
    		{
    			"aliasColors": {},
    			"bars": false,
    			"dashLength": 10,
    			"dashes": false,
    			"datasource": null,
    			"fieldConfig": {
    				"defaults": {
    					"custom": {}
    				},
    				"overrides": []
    			},
    			"fill": 1,
    			"fillGradient": 0,
    			"gridPos": {
    				"h": 8,
    				"w": 6,
    				"x": 0,
    				"y": 64
    			},
    			"hiddenSeries": false,
    			"id": 30,
    			"legend": {
    				"avg": false,
    				"current": false,
    				"max": false,
    				"min": false,
    				"show": true,
    				"total": false,
    				"values": false
    			},
    			"lines": true,
    			"linewidth": 1,
    			"nullPointMode": "null",
    			"options": {
    				"alertThreshold": true
    			},
    			"percentage": false,
    			"pluginVersion": "7.4.2",
    			"pointradius": 2,
    			"points": false,
    			"renderer": "flot",
    			"seriesOverrides": [],
    			"spaceLength": 10,
    			"stack": false,
    			"steppedLine": false,
    			"targets": [
    				{
    					"expr": "process_start_time_seconds{instance=\"$instance\"}",
    					"interval": "",
    					"legendFormat": "",
    					"refId": "A"
    				}
    			],
    			"thresholds": [],
    			"timeFrom": null,
    			"timeRegions": [],
    			"timeShift": null,
    			"title": "process_start_time_seconds",
    			"tooltip": {
    				"shared": true,
    				"sort": 0,
    				"value_type": "individual"
    			},
    			"type": "graph",
    			"xaxis": {
    				"buckets": null,
    				"mode": "time",
    				"name": null,
    				"show": true,
    				"values": []
    			},
    			"yaxes": [
    				{
    					"$hashKey": "object:1059",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				},
    				{
    					"$hashKey": "object:1060",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				}
    			],
    			"yaxis": {
    				"align": false,
    				"alignLevel": null
    			}
    		},
    		{
    			"aliasColors": {},
    			"bars": false,
    			"dashLength": 10,
    			"dashes": false,
    			"datasource": null,
    			"fieldConfig": {
    				"defaults": {
    					"custom": {}
    				},
    				"overrides": []
    			},
    			"fill": 1,
    			"fillGradient": 0,
    			"gridPos": {
    				"h": 8,
    				"w": 6,
    				"x": 6,
    				"y": 64
    			},
    			"hiddenSeries": false,
    			"id": 26,
    			"legend": {
    				"avg": false,
    				"current": false,
    				"max": false,
    				"min": false,
    				"show": true,
    				"total": false,
    				"values": false
    			},
    			"lines": true,
    			"linewidth": 1,
    			"nullPointMode": "null",
    			"options": {
    				"alertThreshold": true
    			},
    			"percentage": false,
    			"pluginVersion": "7.4.2",
    			"pointradius": 2,
    			"points": false,
    			"renderer": "flot",
    			"seriesOverrides": [],
    			"spaceLength": 10,
    			"stack": false,
    			"steppedLine": false,
    			"targets": [
    				{
    					"expr": "process_open_handles{instance=\"$instance\"}",
    					"interval": "",
    					"legendFormat": "",
    					"refId": "A"
    				}
    			],
    			"thresholds": [],
    			"timeFrom": null,
    			"timeRegions": [],
    			"timeShift": null,
    			"title": "process_open_handles",
    			"tooltip": {
    				"shared": true,
    				"sort": 0,
    				"value_type": "individual"
    			},
    			"type": "graph",
    			"xaxis": {
    				"buckets": null,
    				"mode": "time",
    				"name": null,
    				"show": true,
    				"values": []
    			},
    			"yaxes": [
    				{
    					"$hashKey": "object:895",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				},
    				{
    					"$hashKey": "object:896",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				}
    			],
    			"yaxis": {
    				"align": false,
    				"alignLevel": null
    			}
    		},
    		{
    			"aliasColors": {},
    			"bars": false,
    			"dashLength": 10,
    			"dashes": false,
    			"datasource": null,
    			"fieldConfig": {
    				"defaults": {
    					"custom": {}
    				},
    				"overrides": []
    			},
    			"fill": 1,
    			"fillGradient": 0,
    			"gridPos": {
    				"h": 8,
    				"w": 6,
    				"x": 12,
    				"y": 64
    			},
    			"hiddenSeries": false,
    			"id": 28,
    			"legend": {
    				"avg": false,
    				"current": false,
    				"max": false,
    				"min": false,
    				"show": true,
    				"total": false,
    				"values": false
    			},
    			"lines": true,
    			"linewidth": 1,
    			"nullPointMode": "null",
    			"options": {
    				"alertThreshold": true
    			},
    			"percentage": false,
    			"pluginVersion": "7.4.2",
    			"pointradius": 2,
    			"points": false,
    			"renderer": "flot",
    			"seriesOverrides": [],
    			"spaceLength": 10,
    			"stack": false,
    			"steppedLine": false,
    			"targets": [
    				{
    					"expr": "process_num_threads{instance=\"$instance\"}",
    					"interval": "",
    					"legendFormat": "",
    					"refId": "A"
    				}
    			],
    			"thresholds": [],
    			"timeFrom": null,
    			"timeRegions": [],
    			"timeShift": null,
    			"title": "process_num_threads",
    			"tooltip": {
    				"shared": true,
    				"sort": 0,
    				"value_type": "individual"
    			},
    			"type": "graph",
    			"xaxis": {
    				"buckets": null,
    				"mode": "time",
    				"name": null,
    				"show": true,
    				"values": []
    			},
    			"yaxes": [
    				{
    					"$hashKey": "object:977",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				},
    				{
    					"$hashKey": "object:978",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				}
    			],
    			"yaxis": {
    				"align": false,
    				"alignLevel": null
    			}
    		},
    		{
    			"aliasColors": {},
    			"bars": false,
    			"dashLength": 10,
    			"dashes": false,
    			"datasource": null,
    			"fieldConfig": {
    				"defaults": {
    					"custom": {}
    				},
    				"overrides": []
    			},
    			"fill": 1,
    			"fillGradient": 0,
    			"gridPos": {
    				"h": 8,
    				"w": 6,
    				"x": 18,
    				"y": 64
    			},
    			"hiddenSeries": false,
    			"id": 32,
    			"legend": {
    				"avg": false,
    				"current": false,
    				"max": false,
    				"min": false,
    				"show": true,
    				"total": false,
    				"values": false
    			},
    			"lines": true,
    			"linewidth": 1,
    			"nullPointMode": "null",
    			"options": {
    				"alertThreshold": true
    			},
    			"percentage": false,
    			"pluginVersion": "7.4.2",
    			"pointradius": 2,
    			"points": false,
    			"renderer": "flot",
    			"seriesOverrides": [],
    			"spaceLength": 10,
    			"stack": false,
    			"steppedLine": false,
    			"targets": [
    				{
    					"expr": "process_working_set_bytes{instance=\"$instance\"}",
    					"interval": "",
    					"legendFormat": "",
    					"refId": "A"
    				}
    			],
    			"thresholds": [],
    			"timeFrom": null,
    			"timeRegions": [],
    			"timeShift": null,
    			"title": "process_working_set_bytes",
    			"tooltip": {
    				"shared": true,
    				"sort": 0,
    				"value_type": "individual"
    			},
    			"type": "graph",
    			"xaxis": {
    				"buckets": null,
    				"mode": "time",
    				"name": null,
    				"show": true,
    				"values": []
    			},
    			"yaxes": [
    				{
    					"$hashKey": "object:1141",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				},
    				{
    					"$hashKey": "object:1142",
    					"format": "short",
    					"label": null,
    					"logBase": 1,
    					"max": null,
    					"min": null,
    					"show": true
    				}
    			],
    			"yaxis": {
    				"align": false,
    				"alignLevel": null
    			}
    		}
    	],
    	"refresh": false,
    	"schemaVersion": 27,
    	"style": "dark",
    	"tags": [],
    	"templating": {
    		"list": [
    			{
    				"allValue": null,
    				"current": {
    					"selected": false,
    					"text": "ews-liability-service-risks",
    					"value": "ews-liability-service-risks"
    				},
    				"datasource": null,
    				"definition": "label_values(process_private_memory_bytes{}, job)",
    				"description": null,
    				"error": null,
    				"hide": 0,
    				"includeAll": false,
    				"label": "Job",
    				"multi": false,
    				"name": "job",
    				"options": [],
    				"query": {
    					"query": "label_values(process_private_memory_bytes{}, job)",
    					"refId": "StandardVariableQuery"
    				},
    				"refresh": 1,
    				"regex": "/ews-liability.*/",
    				"skipUrlSync": false,
    				"sort": 0,
    				"tagValuesQuery": "",
    				"tags": [],
    				"tagsQuery": "",
    				"type": "query",
    				"useTags": false
    			},
    			{
    				"allValue": null,
    				"current": {
    					"selected": false,
    					"text": "ews-int",
    					"value": "ews-int"
    				},
    				"datasource": null,
    				"definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)",
    				"description": null,
    				"error": null,
    				"hide": 0,
    				"includeAll": false,
    				"label": "Namespace",
    				"multi": false,
    				"name": "namespace",
    				"options": [],
    				"query": {
    					"query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)",
    					"refId": "StandardVariableQuery"
    				},
    				"refresh": 1,
    				"regex": "",
    				"skipUrlSync": false,
    				"sort": 0,
    				"tagValuesQuery": "",
    				"tags": [],
    				"tagsQuery": "",
    				"type": "query",
    				"useTags": false
    			},
    			{
    				"allValue": null,
    				"current": {
    					"selected": false,
    					"text": "************:80",
    					"value": "************:80"
    				},
    				"datasource": null,
    				"definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)",
    				"description": null,
    				"error": null,
    				"hide": 0,
    				"includeAll": false,
    				"label": "Instance",
    				"multi": false,
    				"name": "instance",
    				"options": [],
    				"query": {
    					"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)",
    					"refId": "StandardVariableQuery"
    				},
    				"refresh": 1,
    				"regex": "",
    				"skipUrlSync": false,
    				"sort": 1,
    				"tagValuesQuery": "",
    				"tags": [],
    				"tagsQuery": "",
    				"type": "query",
    				"useTags": false
    			},
    			{
    				"allValue": null,
    				"current": {
    					"selected": false,
    					"text": "ews-liability-service-risks-66dbf5b58-rnmll",
    					"value": "ews-liability-service-risks-66dbf5b58-rnmll"
    				},
    				"datasource": null,
    				"definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)",
    				"description": null,
    				"error": null,
    				"hide": 2,
    				"includeAll": false,
    				"label": "Pod",
    				"multi": false,
    				"name": "pod",
    				"options": [],
    				"query": {
    					"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)",
    					"refId": "StandardVariableQuery"
    				},
    				"refresh": 1,
    				"regex": "",
    				"skipUrlSync": false,
    				"sort": 0,
    				"tagValuesQuery": "",
    				"tags": [],
    				"tagsQuery": "",
    				"type": "query",
    				"useTags": false
    			},
    			{
    				"allValue": null,
    				"current": {
    					"selected": false,
    					"text": "ews-liability-service-risks-66dbf5b58-rnmll",
    					"value": "ews-liability-service-risks-66dbf5b58-rnmll"
    				},
    				"datasource": null,
    				"definition": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)",
    				"description": null,
    				"error": null,
    				"hide": 0,
    				"includeAll": false,
    				"label": "Podname",
    				"multi": false,
    				"name": "podname",
    				"options": [],
    				"query": {
    					"query": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)",
    					"refId": "StandardVariableQuery"
    				},
    				"refresh": 1,
    				"regex": "",
    				"skipUrlSync": false,
    				"sort": 0,
    				"tagValuesQuery": "",
    				"tags": [],
    				"tagsQuery": "",
    				"type": "query",
    				"useTags": false
    			}
    		]
    	},
    	"time": {
    		"from": "now-12h",
    		"to": "now"
    	},
    	"timepicker": {},
    	"timezone": "",
    	"title": "Liability PROD",
    	"uid": "c48syA7z2cccdds",
    	"version": 5
    }
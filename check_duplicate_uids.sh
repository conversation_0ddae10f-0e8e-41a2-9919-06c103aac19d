#!/bin/bash

# Usage: ./check_duplicate_uids_configmap.sh /path/to/search

if [ -z "$1" ]; then
  echo "Usage: $0 /path/to/search"
  exit 1
fi

SEARCH_DIR="$1"
TMP_UID_FILE=$(mktemp)
DUPLICATE_FOUND=false

trap 'rm -f "$TMP_UID_FILE"' EXIT

# Check required tools
if ! command -v yq >/dev/null 2>&1; then
  echo "Error: yq is required (https://github.com/mikefarah/yq)"
  exit 2
fi

if ! command -v jq >/dev/null 2>&1; then
  echo "Error: jq is required."
  exit 2
fi

find "$SEARCH_DIR" -type f \( -name '*.yaml' -o -name '*.yml' \) | while read -r file; do
  # Get all keys under data that end with '.json'
  json_keys=$(yq eval '.data | keys | .[]' "$file" 2>/dev/null | grep '\.json$')

  for key in $json_keys; do
    # Extract the JSON string from the data.<key> entry
    json_content=$(yq eval ".data.\"$key\"" "$file")

    # Parse uid from the JSON content
    uid=$(echo "$json_content" | jq -r '.uid // empty' 2>/dev/null)

    if [[ -n "$uid" ]]; then
      if grep -q "^$uid|" "$TMP_UID_FILE"; then
        echo "❌ Duplicate UID found: '$uid'"
        echo " - First seen in: $(grep "^$uid|" "$TMP_UID_FILE" | cut -d'|' -f2)"
        echo " - Also found in: $file (key: $key)"
        DUPLICATE_FOUND=true
      else
        echo "$uid|$file (key: $key)" >> "$TMP_UID_FILE"
      fi
    fi
  done
done

if [ "$DUPLICATE_FOUND" = true ]; then
  echo "🚫 Duplicate UIDs detected."
  exit 1
else
  echo "✅ No duplicate UIDs found."
  exit 0
fi


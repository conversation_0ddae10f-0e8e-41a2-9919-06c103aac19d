apiVersion: v1
kind: ConfigMap
metadata:
  name: ews-integrator-crm-all
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: ews-crm
data:
  ews-integrator-crm-all.json: |-
    {
        "annotations": {
            "list": [{
                    "builtIn": 1,
                    "datasource": {
                        "type": "datasource",
                        "uid": "grafana"
                    },
                    "enable": true,
                    "hide": true,
                    "iconColor": "rgba(0, 211, 255, 1)",
                    "name": "Annotations & Alerts",
                    "type": "dashboard"
                }
            ]
        },
        "editable": true,
        "gnetId": null,
        "graphTooltip": 0,
        "id": 609,
        "iteration": 1750754969101,
        "links": [],
        "panels": [{
                "collapsed": false,
                "datasource": null,
                "gridPos": {
                    "h": 1,
                    "w": 24,
                    "x": 0,
                    "y": 0
                },
                "id": 2,
                "panels": [],
                "title": "Inbound",
                "type": "row"
            }, {
                "datasource": null,
                "fieldConfig": {
                    "defaults": {
                        "color": {
                            "mode": "palette-classic"
                        },
                        "custom": {
                            "axisLabel": "",
                            "axisPlacement": "auto",
                            "barAlignment": 0,
                            "drawStyle": "line",
                            "fillOpacity": 10,
                            "gradientMode": "none",
                            "hideFrom": {
                                "graph": false,
                                "legend": false,
                                "tooltip": false,
                                "viz": false
                            },
                            "lineInterpolation": "linear",
                            "lineWidth": 1,
                            "pointSize": 5,
                            "scaleDistribution": {
                                "type": "linear"
                            },
                            "showPoints": "never",
                            "spanNulls": false,
                            "stacking": {},
                            "thresholdsStyle": {}
                        },
                        "mappings": [],
                        "thresholds": {
                            "mode": "absolute",
                            "steps": []
                        },
                        "unit": "short"
                    },
                    "overrides": []
                },
                "gridPos": {
                    "h": 8,
                    "w": 6,
                    "x": 0,
                    "y": 1
                },
                "id": 8,
                "options": {
                    "alertThreshold": true,
                    "legend": {
                        "calcs": [],
                        "displayMode": "list",
                        "placement": "right",
                        "showLegend": true
                    },
                    "tooltip": {
                        "mode": "multi",
                        "sort": "none"
                    },
                    "tooltipOptions": {
                        "mode": "single"
                    }
                },
                "pluginVersion": "11.3.0",
                "targets": [{
                        "datasource": {
                            "type": "prometheus",
                            "uid": "prometheus"
                        },
                        "expr": "increase(consumed_messages_count{job=\"ews-integrator-crm-clickhouse\", namespace=\"$namespace\"}[1m]) / 2",
                        "interval": "",
                        "legendFormat": "consumed",
                        "refId": "A"
                    }, {
                        "datasource": {
                            "type": "prometheus",
                            "uid": "prometheus"
                        },
                        "expr": "cached_messages_count{job=\"ews-integrator-crm-clickhouse\", namespace=\"$namespace\"}",
                        "hide": false,
                        "interval": "",
                        "legendFormat": "cached",
                        "refId": "B"
                    }
                ],
                "title": "Click House",
                "type": "timeseries"
            }, {
                "datasource": null,
                "description": "",
                "fieldConfig": {
                    "defaults": {
                        "color": {
                            "mode": "palette-classic"
                        },
                        "custom": {
                            "axisLabel": "",
                            "axisPlacement": "auto",
                            "barAlignment": 0,
                            "drawStyle": "line",
                            "fillOpacity": 10,
                            "gradientMode": "none",
                            "hideFrom": {
                                "graph": false,
                                "legend": false,
                                "tooltip": false,
                                "viz": false
                            },
                            "lineInterpolation": "linear",
                            "lineWidth": 1,
                            "pointSize": 5,
                            "scaleDistribution": {
                                "type": "linear"
                            },
                            "showPoints": "never",
                            "spanNulls": false,
                            "stacking": {},
                            "thresholdsStyle": {}
                        },
                        "mappings": [],
                        "thresholds": {
                            "mode": "absolute",
                            "steps": [{
                                    "color": "green",
                                    "value": null
                                }, {
                                    "color": "red",
                                    "value": 80
                                }
                            ]
                        },
                        "unit": "short"
                    },
                    "overrides": []
                },
                "gridPos": {
                    "h": 8,
                    "w": 7,
                    "x": 6,
                    "y": 1
                },
                "id": 16,
                "options": {
                    "alertThreshold": true,
                    "legend": {
                        "calcs": [],
                        "displayMode": "list",
                        "placement": "right",
                        "showLegend": true
                    },
                    "tooltip": {
                        "mode": "multi",
                        "sort": "none"
                    },
                    "tooltipOptions": {
                        "mode": "single"
                    }
                },
                "pluginVersion": "11.3.0",
                "targets": [{
                        "datasource": {
                            "type": "prometheus",
                            "uid": "prometheus"
                        },
                        "expr": "sum by () (increase(consumed_messages_count{job=\"ews-integrator-crm-update-processor\", namespace=\"$namespace\"}[5m]))",
                        "interval": "",
                        "legendFormat": "consumed",
                        "refId": "A"
                    }, {
                        "datasource": {
                            "type": "prometheus",
                            "uid": "prometheus"
                        },
                        "expr": "sum by (BU, message_type) (increase(processed_messages_count{job=\"ews-integrator-crm-update-processor\", namespace=\"$namespace\"}[5m]))",
                        "hide": false,
                        "interval": "",
                        "legendFormat": "processed {{BU}} {{message_type}}",
                        "refId": "B"
                    }, {
                        "datasource": {
                            "type": "prometheus",
                            "uid": "prometheus"
                        },
                        "expr": "sum by (BU, message_type) (increase(failed_messages_count{job=\"ews-integrator-crm-update-processor\", namespace=\"$namespace\"}[5m]))",
                        "hide": false,
                        "interval": "",
                        "legendFormat": "failed {{BU}} {{message_type}}",
                        "refId": "C"
                    }
                ],
                "title": "Update Processor",
                "type": "timeseries"
            }, {
                "datasource": null,
                "fieldConfig": {
                    "defaults": {
                        "color": {
                            "mode": "palette-classic"
                        },
                        "custom": {
                            "axisLabel": "",
                            "axisPlacement": "auto",
                            "barAlignment": 0,
                            "drawStyle": "line",
                            "fillOpacity": 10,
                            "gradientMode": "none",
                            "hideFrom": {
                                "graph": false,
                                "legend": false,
                                "tooltip": false,
                                "viz": false
                            },
                            "lineInterpolation": "linear",
                            "lineWidth": 1,
                            "pointSize": 5,
                            "scaleDistribution": {
                                "type": "linear"
                            },
                            "showPoints": "never",
                            "spanNulls": false,
                            "stacking": {},
                            "thresholdsStyle": {}
                        },
                        "mappings": [],
                        "thresholds": {
                            "mode": "absolute",
                            "steps": []
                        },
                        "unit": "short"
                    },
                    "overrides": []
                },
                "gridPos": {
                    "h": 8,
                    "w": 6,
                    "x": 13,
                    "y": 1
                },
                "id": 18,
                "options": {
                    "alertThreshold": true,
                    "legend": {
                        "calcs": [],
                        "displayMode": "list",
                        "placement": "right",
                        "showLegend": true
                    },
                    "tooltip": {
                        "mode": "multi",
                        "sort": "none"
                    },
                    "tooltipOptions": {
                        "mode": "single"
                    }
                },
                "pluginVersion": "11.3.0",
                "targets": [{
                        "datasource": {
                            "type": "prometheus",
                            "uid": "prometheus"
                        },
                        "expr": "increase(player_checks_count{job=\"ews-integrator-crm-dl-service\", namespace=\"$namespace\"}[5m])",
                        "hide": false,
                        "interval": "",
                        "legendFormat": "checked_player {{BU}}",
                        "refId": "A"
                    }, {
                        "datasource": {
                            "type": "prometheus",
                            "uid": "prometheus"
                        },
                        "expr": "cached_messages_count{job=\"ews-integrator-crm-dl-service\", namespace=\"$namespace\"}",
                        "hide": false,
                        "interval": "",
                        "legendFormat": "cached {{message_type}}",
                        "refId": "B"
                    }
                ],
                "title": "Dead Letter",
                "type": "timeseries"
            }, {
                "datasource": null,
                "fieldConfig": {
                    "defaults": {
                        "color": {
                            "mode": "palette-classic"
                        },
                        "custom": {
                            "axisLabel": "",
                            "axisPlacement": "auto",
                            "barAlignment": 0,
                            "drawStyle": "line",
                            "fillOpacity": 10,
                            "gradientMode": "none",
                            "hideFrom": {
                                "graph": false,
                                "legend": false,
                                "tooltip": false,
                                "viz": false
                            },
                            "lineInterpolation": "linear",
                            "lineWidth": 1,
                            "pointSize": 5,
                            "scaleDistribution": {
                                "type": "linear"
                            },
                            "showPoints": "never",
                            "spanNulls": false,
                            "stacking": {},
                            "thresholdsStyle": {}
                        },
                        "mappings": [],
                        "thresholds": {
                            "mode": "absolute",
                            "steps": [{
                                    "color": "green",
                                    "value": null
                                }, {
                                    "color": "red",
                                    "value": 80
                                }
                            ]
                        },
                        "unit": "short"
                    },
                    "overrides": []
                },
                "gridPos": {
                    "h": 8,
                    "w": 5,
                    "x": 19,
                    "y": 1
                },
                "id": 20,
                "options": {
                    "alertThreshold": true,
                    "legend": {
                        "calcs": [],
                        "displayMode": "list",
                        "placement": "right",
                        "showLegend": true
                    },
                    "tooltip": {
                        "mode": "multi",
                        "sort": "none"
                    },
                    "tooltipOptions": {
                        "mode": "single"
                    }
                },
                "pluginVersion": "11.3.0",
                "targets": [{
                        "datasource": {
                            "type": "prometheus",
                            "uid": "prometheus"
                        },
                        "expr": "increase(consumed_messages_count{job=\"ews-integrator-crm-dl-retry-scheduler\", namespace=\"$namespace\"}[5m])",
                        "interval": "",
                        "legendFormat": "consumed",
                        "refId": "A"
                    }, {
                        "datasource": {
                            "type": "prometheus",
                            "uid": "prometheus"
                        },
                        "expr": "cached_players_count{job=\"ews-integrator-crm-dl-retry-scheduler\", namespace=\"$namespace\"}",
                        "hide": false,
                        "interval": "",
                        "legendFormat": "cached {{BU}}",
                        "refId": "B"
                    }
                ],
                "title": "Dead Letter Scheduler",
                "type": "timeseries"
            }, {
                "collapsed": false,
                "datasource": null,
                "gridPos": {
                    "h": 1,
                    "w": 24,
                    "x": 0,
                    "y": 9
                },
                "id": 4,
                "panels": [],
                "title": "Outbound",
                "type": "row"
            }, {
                "datasource": null,
                "fieldConfig": {
                    "defaults": {
                        "color": {
                            "mode": "palette-classic"
                        },
                        "custom": {
                            "axisLabel": "",
                            "axisPlacement": "auto",
                            "barAlignment": 0,
                            "drawStyle": "line",
                            "fillOpacity": 10,
                            "gradientMode": "none",
                            "hideFrom": {
                                "graph": false,
                                "legend": false,
                                "tooltip": false,
                                "viz": false
                            },
                            "lineInterpolation": "linear",
                            "lineWidth": 1,
                            "pointSize": 5,
                            "scaleDistribution": {
                                "type": "linear"
                            },
                            "showPoints": "never",
                            "spanNulls": false,
                            "stacking": {},
                            "thresholdsStyle": {}
                        },
                        "mappings": [],
                        "thresholds": {
                            "mode": "absolute",
                            "steps": [{
                                    "color": "green",
                                    "value": null
                                }, {
                                    "color": "red",
                                    "value": 80
                                }
                            ]
                        },
                        "unit": "short"
                    },
                    "overrides": []
                },
                "gridPos": {
                    "h": 6,
                    "w": 6,
                    "x": 0,
                    "y": 10
                },
                "id": 10,
                "options": {
                    "alertThreshold": true,
                    "legend": {
                        "calcs": [],
                        "displayMode": "list",
                        "placement": "right",
                        "showLegend": true
                    },
                    "tooltip": {
                        "mode": "multi",
                        "sort": "none"
                    },
                    "tooltipOptions": {
                        "mode": "single"
                    }
                },
                "pluginVersion": "11.3.0",
                "targets": [{
                        "datasource": {
                            "type": "prometheus",
                            "uid": "prometheus"
                        },
                        "expr": "increase(processed_messages_count{job=\"ews-integrator-crm-transactions-processor\", namespace=\"$namespace\"}[1m]) / 2",
                        "hide": false,
                        "interval": "",
                        "legendFormat": "processed {{BU}} {{message_type}}",
                        "refId": "B"
                    }
                ],
                "title": "Transactions Processor",
                "type": "timeseries"
            }, {
                "datasource": null,
                "fieldConfig": {
                    "defaults": {
                        "color": {
                            "mode": "palette-classic"
                        },
                        "custom": {
                            "axisLabel": "",
                            "axisPlacement": "auto",
                            "barAlignment": 0,
                            "drawStyle": "line",
                            "fillOpacity": 10,
                            "gradientMode": "none",
                            "hideFrom": {
                                "graph": false,
                                "legend": false,
                                "tooltip": false,
                                "viz": false
                            },
                            "lineInterpolation": "linear",
                            "lineWidth": 1,
                            "pointSize": 5,
                            "scaleDistribution": {
                                "type": "linear"
                            },
                            "showPoints": "never",
                            "spanNulls": false,
                            "stacking": {},
                            "thresholdsStyle": {}
                        },
                        "mappings": [],
                        "thresholds": {
                            "mode": "absolute",
                            "steps": [{
                                    "color": "green",
                                    "value": null
                                }, {
                                    "color": "red",
                                    "value": 80
                                }
                            ]
                        },
                        "unit": "short"
                    },
                    "overrides": []
                },
                "gridPos": {
                    "h": 6,
                    "w": 6,
                    "x": 6,
                    "y": 10
                },
                "id": 22,
                "options": {
                    "alertThreshold": true,
                    "legend": {
                        "calcs": [],
                        "displayMode": "list",
                        "placement": "right",
                        "showLegend": true
                    },
                    "tooltip": {
                        "mode": "multi",
                        "sort": "none"
                    },
                    "tooltipOptions": {
                        "mode": "single"
                    }
                },
                "pluginVersion": "11.3.0",
                "targets": [{
                        "datasource": {
                            "type": "prometheus",
                            "uid": "prometheus"
                        },
                        "expr": "increase(consumed_messages_count{job=\"ews-integrator-crm-outbound-routing\", namespace=\"$namespace\"}[1m]) / 2",
                        "interval": "",
                        "legendFormat": "consumed",
                        "refId": "A"
                    }, {
                        "datasource": {
                            "type": "prometheus",
                            "uid": "prometheus"
                        },
                        "expr": "cached_messages_count{job=\"ews-integrator-crm-outbound-routing\", namespace=\"$namespace\"}",
                        "hide": false,
                        "interval": "",
                        "legendFormat": "cached",
                        "refId": "B"
                    }, {
                        "datasource": {
                            "type": "prometheus",
                            "uid": "prometheus"
                        },
                        "expr": "increase(processed_messages_count{job=\"ews-integrator-crm-outbound-routing\", namespace=\"$namespace\"}[1m]) / 2",
                        "hide": false,
                        "interval": "",
                        "legendFormat": "processed {{BU}} {{message_type}}",
                        "refId": "C"
                    }
                ],
                "title": "Outbound Router",
                "type": "timeseries"
            }, {
                "datasource": null,
                "description": "",
                "fieldConfig": {
                    "defaults": {
                        "color": {
                            "mode": "palette-classic"
                        },
                        "custom": {
                            "axisLabel": "",
                            "axisPlacement": "auto",
                            "barAlignment": 0,
                            "drawStyle": "line",
                            "fillOpacity": 10,
                            "gradientMode": "none",
                            "hideFrom": {
                                "graph": false,
                                "legend": false,
                                "tooltip": false,
                                "viz": false
                            },
                            "lineInterpolation": "linear",
                            "lineWidth": 1,
                            "pointSize": 5,
                            "scaleDistribution": {
                                "type": "linear"
                            },
                            "showPoints": "never",
                            "spanNulls": false,
                            "stacking": {},
                            "thresholdsStyle": {}
                        },
                        "mappings": [],
                        "thresholds": {
                            "mode": "absolute",
                            "steps": [{
                                    "color": "green",
                                    "value": null
                                }, {
                                    "color": "red",
                                    "value": 80
                                }
                            ]
                        },
                        "unit": "short"
                    },
                    "overrides": []
                },
                "gridPos": {
                    "h": 6,
                    "w": 6,
                    "x": 12,
                    "y": 10
                },
                "id": 12,
                "options": {
                    "alertThreshold": true,
                    "legend": {
                        "calcs": [],
                        "displayMode": "list",
                        "placement": "bottom",
                        "showLegend": true
                    },
                    "tooltip": {
                        "mode": "multi",
                        "sort": "none"
                    },
                    "tooltipOptions": {
                        "mode": "single"
                    }
                },
                "pluginVersion": "11.3.0",
                "targets": [{
                        "datasource": {
                            "type": "prometheus",
                            "uid": "prometheus"
                        },
                        "expr": "increase(endpoint_hit_count{job=\"ews-integrator-crm-outbound-facade\", namespace=\"$namespace\"}[1m])",
                        "interval": "",
                        "legendFormat": "{{endpoint}}",
                        "refId": "A"
                    }
                ],
                "title": "Crm Facade",
                "type": "timeseries"
            }, {
                "datasource": null,
                "fieldConfig": {
                    "defaults": {
                        "color": {
                            "mode": "palette-classic"
                        },
                        "custom": {
                            "axisLabel": "",
                            "axisPlacement": "auto",
                            "barAlignment": 0,
                            "drawStyle": "line",
                            "fillOpacity": 10,
                            "gradientMode": "none",
                            "hideFrom": {
                                "graph": false,
                                "legend": false,
                                "tooltip": false,
                                "viz": false
                            },
                            "lineInterpolation": "linear",
                            "lineWidth": 1,
                            "pointSize": 5,
                            "scaleDistribution": {
                                "type": "linear"
                            },
                            "showPoints": "never",
                            "spanNulls": false,
                            "stacking": {},
                            "thresholdsStyle": {}
                        },
                        "mappings": [],
                        "thresholds": {
                            "mode": "absolute",
                            "steps": [{
                                    "color": "green",
                                    "value": null
                                }, {
                                    "color": "red",
                                    "value": 80
                                }
                            ]
                        },
                        "unit": "short"
                    },
                    "overrides": []
                },
                "gridPos": {
                    "h": 6,
                    "w": 6,
                    "x": 18,
                    "y": 10
                },
                "id": 26,
                "options": {
                    "alertThreshold": true,
                    "legend": {
                        "calcs": [],
                        "displayMode": "list",
                        "placement": "bottom",
                        "showLegend": true
                    },
                    "tooltip": {
                        "mode": "multi",
                        "sort": "none"
                    },
                    "tooltipOptions": {
                        "mode": "single"
                    }
                },
                "pluginVersion": "11.3.0",
                "targets": [{
                        "datasource": {
                            "type": "prometheus",
                            "uid": "prometheus"
                        },
                        "expr": "increase(bu_endpoint_hit_count{job=\"ews-integrator-crm-players-facade\", namespace=\"$namespace\"}[1m])",
                        "interval": "",
                        "legendFormat": "{{BU}} {{endpoint}}",
                        "refId": "A"
                    }
                ],
                "title": "Players Facade",
                "type": "timeseries"
            }, {
                "collapsed": false,
                "datasource": null,
                "gridPos": {
                    "h": 1,
                    "w": 24,
                    "x": 0,
                    "y": 16
                },
                "id": 24,
                "panels": [],
                "title": "Errors",
                "type": "row"
            }, {
                "datasource": null,
                "fieldConfig": {
                    "defaults": {
                        "color": {
                            "mode": "palette-classic"
                        },
                        "custom": {
                            "axisLabel": "",
                            "axisPlacement": "auto",
                            "barAlignment": 0,
                            "drawStyle": "line",
                            "fillOpacity": 10,
                            "gradientMode": "none",
                            "hideFrom": {
                                "graph": false,
                                "legend": false,
                                "tooltip": false,
                                "viz": false
                            },
                            "lineInterpolation": "linear",
                            "lineWidth": 1,
                            "pointSize": 5,
                            "scaleDistribution": {
                                "type": "linear"
                            },
                            "showPoints": "never",
                            "spanNulls": false,
                            "stacking": {},
                            "thresholdsStyle": {}
                        },
                        "mappings": [],
                        "thresholds": {
                            "mode": "absolute",
                            "steps": [{
                                    "color": "green",
                                    "value": null
                                }, {
                                    "color": "red",
                                    "value": 80
                                }
                            ]
                        },
                        "unit": "short"
                    },
                    "overrides": [{
                            "matcher": {
                                "id": "byName",
                                "options": "error ews-integrator-crm-dummy-producer"
                            },
                            "properties": [{
                                    "id": "color",
                                    "value": {
                                        "fixedColor": "dark-red",
                                        "mode": "fixed"
                                    }
                                }
                            ]
                        }
                    ]
                },
                "gridPos": {
                    "h": 4,
                    "w": 24,
                    "x": 0,
                    "y": 17
                },
                "id": 14,
                "options": {
                    "alertThreshold": true,
                    "legend": {
                        "calcs": [],
                        "displayMode": "list",
                        "placement": "right",
                        "showLegend": true
                    },
                    "tooltip": {
                        "mode": "multi",
                        "sort": "none"
                    },
                    "tooltipOptions": {
                        "mode": "single"
                    }
                },
                "pluginVersion": "11.3.0",
                "targets": [{
                        "datasource": {
                            "type": "prometheus",
                            "uid": "prometheus"
                        },
                        "expr": "sum by (job) (increase(error_count{job=~\"ews-integrator-crm-.*\", namespace=\"$namespace\"}[5m]))",
                        "hide": false,
                        "interval": "",
                        "legendFormat": "error {{job}}",
                        "refId": "error_count"
                    }, {
                        "datasource": {
                            "type": "prometheus",
                            "uid": "prometheus"
                        },
                        "disableTextWrap": false,
                        "editorMode": "code",
                        "expr": "sum by (job) (increase(kafka_errors{job=~\"ews-integrator-crm-.*\", namespace=\"$namespace\"}[5m]))",
                        "fullMetaSearch": false,
                        "hide": false,
                        "includeNullMetadata": true,
                        "instant": false,
                        "interval": "",
                        "legendFormat": "kafka-error {{job}}",
                        "range": true,
                        "refId": "kafka_errors",
                        "useBackend": false
                    }
                ],
                "title": "Errors",
                "type": "timeseries"
            }
        ],
        "refresh": "5s",
        "schemaVersion": 27,
        "style": "dark",
        "tags": [],
        "templating": {
            "list": [{
                    "allValue": null,
                    "current": {
                        "selected": false,
                        "text": "ews-integrator-crm-clickhouse",
                        "value": "ews-integrator-crm-clickhouse"
                    },
                    "datasource": null,
                    "definition": "label_values(process_private_memory_bytes{}, job)",
                    "description": null,
                    "error": null,
                    "hide": 0,
                    "includeAll": false,
                    "label": "Job",
                    "multi": false,
                    "name": "job",
                    "options": [],
                    "query": {
                        "query": "label_values(process_private_memory_bytes{}, job)",
                        "refId": "StandardVariableQuery"
                    },
                    "refresh": 1,
                    "regex": "/ews-integrator-crm-|ews-stream-mirroring-crm*/",
                    "skipUrlSync": false,
                    "sort": 0,
                    "tagValuesQuery": "",
                    "tags": [],
                    "tagsQuery": "",
                    "type": "query",
                    "useTags": false
                }, {
                    "allValue": null,
                    "current": {
                        "selected": false,
                        "text": "ews-sof-prod",
                        "value": "ews-sof-prod"
                    },
                    "datasource": null,
                    "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)",
                    "description": null,
                    "error": null,
                    "hide": 0,
                    "includeAll": false,
                    "label": "Namespace",
                    "multi": false,
                    "name": "namespace",
                    "options": [],
                    "query": {
                        "query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)",
                        "refId": "StandardVariableQuery"
                    },
                    "refresh": 1,
                    "regex": "",
                    "skipUrlSync": false,
                    "sort": 0,
                    "tagValuesQuery": "",
                    "tags": [],
                    "tagsQuery": "",
                    "type": "query",
                    "useTags": false
                }
            ]
        },
        "time": {
            "from": "now-18d",
            "to": "now"
        },
        "timepicker": {},
        "timezone": "",
        "title": "integrator crm",
        "uid": "d844ab339d824f47a280",
        "version": 3
    }

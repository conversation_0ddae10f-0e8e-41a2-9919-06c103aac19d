apiVersion: v1
kind: ConfigMap
metadata:
  name: nra-service-failure
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: infrastructure
data:
  nra-service-failure.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": 479,
      "links": [],
      "panels": [
        {
          "datasource": "PostgreSQL-remote-db",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {
                "align": null,
                "filterable": false
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 18,
            "w": 12,
            "x": 0,
            "y": 0
          },
          "id": 4,
          "options": {
            "showHeader": true
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "format": "table",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "select\nid, create_date, message\nfrom regulation_report.reporting_activity_bg\nwhere\nbusiness_unit = 'WBBG'\nand status_id = (select id from regulation_report.reporting_activity_statuses rs where rs.name = 'Error')\nand create_date >= ((now() at time zone 'utc') - interval '1 hours')",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "value"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "timeColumn": "time",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "title": "NRA Service failures during sending table",
          "type": "table"
        },
        {
          "datasource": "PostgreSQL",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "thresholds"
              },
              "custom": {
                "align": null,
                "filterable": false
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              }
            },
            "overrides": []
          },
          "gridPos": {
            "h": 18,
            "w": 12,
            "x": 12,
            "y": 0
          },
          "id": 8,
          "options": {
            "showHeader": true
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "format": "table",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "select created_on, type, direction, file_link from player.regulator_files_data where buisness_unit = 'WBBG' and is_ok = false\nand created_on >= ((now() at time zone 'utc') - interval '1 hours')\n",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "value"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "timeColumn": "time",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "title": "NRA Service failures for the last hour table",
          "type": "table"
        }
      ],
      "refresh": "5m",
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": []
      },
      "time": {
        "from": "now-24h",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "NRA Service Failure",
      "uid": "sX5E1-f7k",
      "version": 1
    }
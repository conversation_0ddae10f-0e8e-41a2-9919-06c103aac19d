apiVersion: v1
kind: ConfigMap
metadata:
  name: audit-api-dashboard
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: ews-infra
data:
  audit-api-dashboard.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": 936,
      "iteration": 1751896038554,
      "links": [],
      "panels": [
        {
          "collapsed": false,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "id": 22,
          "panels": [],
          "title": "General audit telemetry",
          "type": "row"
        },
        {
          "datasource": null,
          "description": "This is general metric to trace endpoints calls in order to show API activities",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 7,
            "w": 12,
            "x": 0,
            "y": 1
          },
          "id": 2,
          "options": {
            "graph": {},
            "legend": {
              "calcs": [],
              "displayMode": "table",
              "placement": "right"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "expr": "sum by (service_name) (increase(audit_logs_api_different_endpoints_hit_count [5m])) > 0",
              "interval": "",
              "legendFormat": "Service: {{service_name}}",
              "refId": "A"
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "API Hits Counter",
          "type": "timeseries"
        },
        {
          "datasource": null,
          "description": "Audit Collector errors on validations and kafka. The sum is made by product and front-end location. To see which product or front-end page is faling.",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 7,
            "w": 12,
            "x": 12,
            "y": 1
          },
          "id": 24,
          "options": {
            "graph": {},
            "legend": {
              "calcs": [],
              "displayMode": "table",
              "placement": "right"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "expr": "sum by (product) (increase(audit_logs_validation_errors_counter[5m])) > 0",
              "interval": "",
              "legendFormat": "Validation failed - {{product}}",
              "refId": "A"
            },
            {
              "expr": "sum by (product ) (increase(audit_kafka_errors_produced_counter[5m])) > 0",
              "hide": false,
              "interval": "",
              "legendFormat": "Kafka failure - {{product}} ",
              "refId": "B"
            },
            {
              "expr": "sum by (product) (increase(audits_kafka_successful_counter[5m])) > 0",
              "hide": false,
              "interval": "",
              "legendFormat": "Produced - {{product}}",
              "refId": "C"
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Collector - Validation/Kafka Errors",
          "type": "timeseries"
        },
        {
          "datasource": null,
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 7,
            "w": 12,
            "x": 0,
            "y": 8
          },
          "id": 30,
          "options": {
            "graph": {},
            "legend": {
              "calcs": [],
              "displayMode": "table",
              "placement": "right"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "expr": "sum by (service,type) (increase(audit_logs_failed_store_counter[5m])) > 0",
              "interval": "",
              "legendFormat": "\"{{service}}\" failed with - {{type}}",
              "refId": "A"
            },
            {
              "expr": "rate(audit_logs_store_counter[5m]) - rate(audit_logs_produced_store_counter[5m]) > 0",
              "hide": false,
              "interval": "",
              "legendFormat": "Stored for: \"{{service}}\"",
              "refId": "B"
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Producer Telemetry - Stored/Failed",
          "type": "timeseries"
        },
        {
          "datasource": null,
          "description": "Keeps track of successfully produced audit logs summed by product and service. This is activity tracker.",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 7,
            "w": 12,
            "x": 12,
            "y": 8
          },
          "id": 28,
          "options": {
            "graph": {},
            "legend": {
              "calcs": [],
              "displayMode": "table",
              "placement": "right"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "expr": "sum by (service, product) (increase(audit_logs_produced_store_counter[5m])) > 0",
              "interval": "",
              "legendFormat": "{{product}}  -  {{service}}",
              "refId": "A"
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Produced Logs by product/service",
          "type": "timeseries"
        },
        {
          "collapsed": false,
          "datasource": null,
          "gridPos": {
            "h": 1,
            "w": 24,
            "x": 0,
            "y": 15
          },
          "id": 34,
          "panels": [],
          "title": "Errors",
          "type": "row"
        },
        {
          "datasource": null,
          "description": "This is total counter for errors split only by  service. Just to trace whether there are some errors in the app",
          "fieldConfig": {
            "defaults": {
              "color": {
                "mode": "palette-classic"
              },
              "custom": {
                "axisLabel": "",
                "axisPlacement": "auto",
                "barAlignment": 0,
                "drawStyle": "line",
                "fillOpacity": 10,
                "gradientMode": "none",
                "hideFrom": {
                  "graph": false,
                  "legend": false,
                  "tooltip": false
                },
                "lineInterpolation": "linear",
                "lineWidth": 1,
                "pointSize": 5,
                "scaleDistribution": {
                  "type": "linear"
                },
                "showPoints": "never",
                "spanNulls": true
              },
              "mappings": [],
              "thresholds": {
                "mode": "absolute",
                "steps": [
                  {
                    "color": "green",
                    "value": null
                  },
                  {
                    "color": "red",
                    "value": 80
                  }
                ]
              },
              "unit": "short"
            },
            "overrides": []
          },
          "gridPos": {
            "h": 8,
            "w": 24,
            "x": 0,
            "y": 16
          },
          "id": 32,
          "options": {
            "graph": {},
            "legend": {
              "calcs": [],
              "displayMode": "table",
              "placement": "right"
            },
            "tooltipOptions": {
              "mode": "single"
            }
          },
          "pluginVersion": "7.4.2",
          "targets": [
            {
              "expr": "sum by (service_name) (increase(audit_logs_api_total_error_count[5m])) > 0",
              "interval": "",
              "legendFormat": "{{service_name}}",
              "refId": "A"
            }
          ],
          "timeFrom": null,
          "timeShift": null,
          "title": "Errors Total Count",
          "type": "timeseries"
        }
      ],
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": [
          {
            "allValue": null,
            "current": {
              "selected": false,
              "text": "ews-audit-api",
              "value": "ews-audit-api"
            },
            "datasource": null,
            "definition": "label_values(process_private_memory_bytes{}, job)",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": false,
            "label": "Job",
            "multi": false,
            "name": "job",
            "options": [],
            "query": {
              "query": "label_values(process_private_memory_bytes{}, job)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "/ews-audit-.*/",
            "skipUrlSync": false,
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {
              "selected": false,
              "text": "ews-sof-prod",
              "value": "ews-sof-prod"
            },
            "datasource": null,
            "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": false,
            "label": "Namespace",
            "multi": false,
            "name": "namespace",
            "options": [],
            "query": {
              "query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {
              "selected": false,
              "text": "*************:8080",
              "value": "*************:8080"
            },
            "datasource": null,
            "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": false,
            "label": "Instance",
            "multi": false,
            "name": "instance",
            "options": [],
            "query": {
              "query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 1,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {
              "selected": false,
              "text": "ews-audit-api-56bc974d9f-2w2nh",
              "value": "ews-audit-api-56bc974d9f-2w2nh"
            },
            "datasource": null,
            "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)",
            "description": null,
            "error": null,
            "hide": 2,
            "includeAll": false,
            "label": "Pod",
            "multi": false,
            "name": "pod",
            "options": [],
            "query": {
              "query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          },
          {
            "allValue": null,
            "current": {
              "selected": false,
              "text": "ews-audit-api-56bc974d9f-2w2nh",
              "value": "ews-audit-api-56bc974d9f-2w2nh"
            },
            "datasource": null,
            "definition": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)",
            "description": null,
            "error": null,
            "hide": 0,
            "includeAll": false,
            "label": "Podname",
            "multi": false,
            "name": "podname",
            "options": [],
            "query": {
              "query": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)",
              "refId": "StandardVariableQuery"
            },
            "refresh": 1,
            "regex": "",
            "skipUrlSync": false,
            "sort": 0,
            "tagValuesQuery": "",
            "tags": [],
            "tagsQuery": "",
            "type": "query",
            "useTags": false
          }
        ]
      },
      "time": {
        "from": "now-1h",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "ews-log-audit",
      "uid": "bbdc5c74effc4db6bbba",
      "version": 2
    }
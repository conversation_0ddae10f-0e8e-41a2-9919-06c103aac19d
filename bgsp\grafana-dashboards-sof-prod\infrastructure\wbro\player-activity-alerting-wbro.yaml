apiVersion: v1
kind: ConfigMap
metadata:
  name: player-activity-alerting-wbro
  namespace: infrastructure
  labels:
    grafana_dashboard: "true"
  annotations:
    k8s-sidecar-target-directory: WBRO
data:
  player-activity-alerting-wbro.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": 444,
      "links": [],
      "panels": [
        {
          "alert": {
            "alertRuleTags": {
              "severity": "critical"
            },
            "conditions": [
              {
                "evaluator": {
                  "params": [
                    10
                  ],
                  "type": "lt"
                },
                "operator": {
                  "type": "and"
                },
                "query": {
                  "params": [
                    "C",
                    "5m",
                    "now"
                  ]
                },
                "reducer": {
                  "params": [],
                  "type": "avg"
                },
                "type": "query"
              }
            ],
            "executionErrorState": "alerting",
            "for": "5m",
            "frequency": "1m",
            "handler": 1,
            "name": "Player activity EGT Interactive - WBRO",
            "noDataState": "alerting",
            "notifications": [
              {
                "uid": "I9C9By5Vk"
              }
            ]
          },
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "audit-logs-monitoring-db",
          "fieldConfig": {
            "defaults": {
              "custom": {},
              "unit": "none"
            },
            "overrides": [
              {
                "matcher": {
                  "id": "byName",
                  "options": "1"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "EGT Interactive"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "10"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "NetEnt"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "11"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Amatic"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "12"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Casino Technology (direct)"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "14"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Habanero"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "2"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "EGT Digital Games"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "22"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Scientific Games Digital"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "3"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Evolution"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "36"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Playtech"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "4"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Playson"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "42"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Red Tiger"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "43"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "NetEnt"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "6"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Spribe"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "7"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Evoplay"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "8"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Pragmatic"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "9"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Endorphina"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "5"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Isofbet"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "38"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Bee-Fee"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "37"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "EGT Digital Instant"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "35"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Arc"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "34"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Elbet Virtual Games"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "33"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Wazdan"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "32"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Fazi"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "31"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "NSoft"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "30"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "SkyWind"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "29"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "SpadeGaming"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "28"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Red Rake"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "27"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Betgames"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "26"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "Stake Logic"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "25"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "TVBet"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "24"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "CT Gaming"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "23"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "MrSlotty"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "21"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "PSO"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "20"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "CDB"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "19"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "KLM"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "18"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "GIVME"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "17"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "CHG"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "16"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "GAM"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "15"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "ORYX"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "13"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "EGT Digital Lottary"
                  }
                ]
              },
              {
                "matcher": {
                  "id": "byName",
                  "options": "46"
                },
                "properties": [
                  {
                    "id": "displayName",
                    "value": "7777 Gaming"
                  }
                ]
              }
            ]
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "hiddenSeries": false,
          "id": 2,
          "legend": {
            "alignAsTable": true,
            "avg": true,
            "current": true,
            "hideZero": true,
            "max": true,
            "min": true,
            "rightSide": true,
            "show": true,
            "sideWidth": 400,
            "sort": "max",
            "sortDesc": true,
            "total": false,
            "values": true
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "options": {
            "alertThreshold": true
          },
          "percentage": false,
          "pluginVersion": "7.4.2",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "format": "time_series",
              "group": [],
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  activity_time AS \"time\",\n  game_provider_id::text AS metric,\n  player_count\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time) AND game_provider_id != 0 AND game_provider_id != 1 AND game_provider_id != 2  and business_unit = 'WBRO'\nORDER BY 1,2",
              "refId": "A",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            },
            {
              "format": "time_series",
              "group": [],
              "hide": false,
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  activity_time AS \"time\",\n  sum(player_count) AS \"Total\"\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time) AND game_provider_id != 0 and business_unit = 'WBRO'\nGROUP BY 1\nORDER BY 1",
              "refId": "B",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            },
            {
              "format": "time_series",
              "group": [],
              "hide": false,
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  activity_time AS \"time\",\n  game_provider_id::text AS metric,\n  player_count\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time) AND game_provider_id = 1 and business_unit = 'WBRO'\nORDER BY 1,2",
              "refId": "C",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            },
            {
              "format": "time_series",
              "group": [],
              "hide": false,
              "metricColumn": "none",
              "rawQuery": true,
              "rawSql": "SELECT\n  activity_time AS \"time\",\n  game_provider_id::text AS metric,\n  player_count\nFROM player_activity\nWHERE\n  $__timeFilter(activity_time) AND game_provider_id = 2 and business_unit = 'WBRO'\nORDER BY 1,2",
              "refId": "D",
              "select": [
                [
                  {
                    "params": [
                      "redis_wait"
                    ],
                    "type": "column"
                  }
                ]
              ],
              "table": "sport_offering_timing",
              "timeColumn": "date",
              "timeColumnType": "timestamp",
              "where": [
                {
                  "name": "$__timeFilter",
                  "params": [],
                  "type": "macro"
                }
              ]
            }
          ],
          "thresholds": [
            {
              "colorMode": "critical",
              "fill": true,
              "line": true,
              "op": "lt",
              "value": 10,
              "visible": true
            }
          ],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Unique users with bets per minute, CASINO",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "$$hashKey": "object:82",
              "format": "none",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "$$hashKey": "object:83",
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        }
      ],
      "refresh": "5m",
      "schemaVersion": 27,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": []
      },
      "time": {
        "from": "now-7d",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "Alerting for player activity - WBRO",
      "uid": "H4dm6wW4kwbro",
      "version": 1
    }